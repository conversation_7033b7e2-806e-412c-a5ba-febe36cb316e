<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>GIS Shapefile 导入工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>🚀 GIS 数据导入系统</h1>

    <!-- 导航菜单 -->
    <div style="margin-bottom: 30px; padding: 15px; background-color: #ecf0f1; border-radius: 5px;">
        <h3>功能导航</h3>
        <a href="index.html" style="margin-right: 20px; color: #3498db; text-decoration: none; font-weight: bold;">🏠 系统首页</a>
        <a href="file-upload.html" style="margin-right: 20px; color: #3498db; text-decoration: none;">📤 文件上传</a>
        <a href="task-management.html" style="margin-right: 20px; color: #3498db; text-decoration: none;">📋 任务管理</a>
        <a href="template-upload.html" style="margin-right: 20px; color: #3498db; text-decoration: none;">⚙️ 模板配置</a>
        <a href="coordinate-transform.html" style="color: #3498db; text-decoration: none;">🗺️ 坐标转换</a>
    </div>

    <!-- 系统介绍 -->
    <div style="background-color: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 30px; border-left: 4px solid #007bff;">
        <h2 style="color: #0056b3; margin-top: 0;">📋 系统说明</h2>
        <p style="margin-bottom: 15px; line-height: 1.6;">
            本系统专门用于<strong>基于模板的高性能GIS数据导入</strong>，支持Shapefile格式数据的批量处理和坐标转换。
        </p>
        <div style="background-color: #fff3cd; padding: 15px; border-radius: 4px; border: 1px solid #ffeaa7;">
            <h4 style="color: #856404; margin-top: 0;">⚠️ 重要提示</h4>
            <ul style="margin: 0; padding-left: 20px; color: #856404;">
                <li>系统<strong>不再支持</strong>无模板的基础上传功能</li>
                <li>所有数据导入<strong>必须使用模板配置</strong></li>
                <li>模板必须配置<strong>有效的目标表名</strong>，不支持默认表名</li>
                <li>请使用"模板化数据导入"功能进行数据处理</li>
            </ul>
        </div>
    </div>

    <!-- 功能特性 -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h3 style="color: #28a745; margin-top: 0;">🚀 高性能处理</h3>
            <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                <li>零转换超高速批量插入</li>
                <li>智能字段映射和类型转换</li>
                <li>支持大数据量并发处理</li>
                <li>自动错误恢复机制</li>
            </ul>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h3 style="color: #17a2b8; margin-top: 0;">🗺️ 坐标转换</h3>
            <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                <li>支持多种坐标系转换</li>
                <li>模板化坐标转换配置</li>
                <li>批量坐标处理</li>
                <li>精确的转换算法</li>
            </ul>
        </div>
    </div>

    <!-- 快速开始 -->
    <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; border: 1px solid #c3e6cb;">
        <h3 style="color: #155724; margin-top: 0;">🎯 快速开始</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
            <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 10px;">1️⃣</div>
                <strong>准备模板</strong><br>
                <small>配置字段映射和目标表名</small>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 10px;">2️⃣</div>
                <strong>上传数据</strong><br>
                <small>选择Shapefile ZIP文件</small>
            </div>
            <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 10px;">3️⃣</div>
                <strong>高速导入</strong><br>
                <small>自动处理和插入数据库</small>
            </div>
        </div>
        <div style="text-align: center; margin-top: 20px;">
            <a href="file-upload.html" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-right: 15px;">
                📤 开始文件上传
            </a>
            <a href="task-management.html" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                📋 查看任务管理
            </a>
        </div>
    </div>

    <!-- 技术支持 -->
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 30px; border: 1px solid #dee2e6;">
        <h3 style="color: #6c757d; margin-top: 0;">📞 技术支持</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4 style="color: #495057; margin-bottom: 10px;">支持的数据格式</h4>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li>Shapefile (.shp, .dbf, .shx)</li>
                    <li>ZIP压缩包格式</li>
                    <li>GBK/UTF-8编码支持</li>
                </ul>
            </div>
            <div>
                <h4 style="color: #495057; margin-bottom: 10px;">支持的坐标系</h4>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.6;">
                    <li>CGCS2000 (国家坐标系)</li>
                    <li>WenZhou2000 (温州坐标系)</li>
                    <li>Beijing1954 (北京坐标系)</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('GIS数据导入系统已加载');

            // 检查浏览器兼容性
            if (!window.fetch) {
                alert('您的浏览器版本过低，请升级到现代浏览器以获得最佳体验。');
            }
        });
    </script>
</body>
</html>
