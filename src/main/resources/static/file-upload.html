<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GIS数据导入系统 - 文件上传</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover, .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .file-info {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        .progress-container {
            margin-top: 20px;
        }
        .task-status {
            padding: 10px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .status-pending { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .status-validating { background-color: #cff4fc; border-left: 4px solid #0dcaf0; }
        .status-success { background-color: #d1e7dd; border-left: 4px solid #198754; }
        .status-error { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .validation-summary {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 20px;
        }
        .metric-card {
            text-align: center;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row">
            <div class="col-12">
                <h2><i class="bi bi-cloud-upload"></i> GIS数据导入系统</h2>
                <p class="text-muted">支持Excel (.xlsx, .xls) 和 Shapefile (.zip) 格式的数据导入</p>
            </div>
        </div>

        <!-- 文件上传区域 -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-file-earmark-arrow-up"></i> 文件上传</h5>
                    </div>
                    <div class="card-body">
                        <!-- 模板选择 -->
                        <div class="mb-3">
                            <label for="templateSelect" class="form-label">选择导入模板 *</label>
                            <select class="form-select" id="templateSelect" required>
                                <option value="">请选择模板...</option>
                            </select>
                        </div>

                        <!-- 任务名称 -->
                        <div class="mb-3">
                            <label for="taskName" class="form-label">任务名称</label>
                            <input type="text" class="form-control" id="taskName" placeholder="可选，系统将自动生成">
                        </div>

                        <!-- 文件上传区域 -->
                        <div class="upload-area" id="uploadArea">
                            <i class="bi bi-cloud-upload" style="font-size: 3rem; color: #6c757d;"></i>
                            <h5 class="mt-3">拖拽文件到此处或点击选择文件</h5>
                            <p class="text-muted">支持 .xlsx, .xls, .zip 格式，最大 500MB</p>
                            <input type="file" id="fileInput" class="d-none" accept=".xlsx,.xls,.zip">
                        </div>

                        <!-- 文件信息显示 -->
                        <div id="fileInfo" class="file-info d-none">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="bi bi-file-earmark"></i>
                                    <span id="fileName"></span>
                                    <small class="text-muted">(<span id="fileSize"></span>)</small>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="removeFile">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 上传进度 -->
                        <div id="uploadProgress" class="progress-container d-none">
                            <div class="d-flex justify-content-between mb-2">
                                <span>上传进度</span>
                                <span id="progressText">0%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary" id="uploadBtn" disabled>
                                <i class="bi bi-upload"></i> 创建任务并上传
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" id="resetBtn">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务状态面板 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-list-task"></i> 任务状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="taskStatus" class="text-center text-muted">
                            <i class="bi bi-info-circle"></i>
                            <p class="mt-2">请先上传文件创建任务</p>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="bi bi-lightning"></i> 快速操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="task-list.html" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-list"></i> 查看任务列表
                            </a>
                            <a href="template-upload.html" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-gear"></i> 模板管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 验证结果展示区域 -->
        <div id="validationResults" class="d-none">
            <div class="validation-summary">
                <h5><i class="bi bi-check-circle"></i> 验证结果</h5>
                <div class="row" id="validationMetrics">
                    <!-- 动态生成验证指标 -->
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-success" id="executeImportBtn" disabled>
                        <i class="bi bi-play-fill"></i> 执行导入
                    </button>
                    <button type="button" class="btn btn-outline-warning" id="downloadErrorReportBtn" disabled>
                        <i class="bi bi-download"></i> 下载错误报告
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle me-2"></i>
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- 动态消息内容 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/file-upload.js"></script>
</body>
</html>
