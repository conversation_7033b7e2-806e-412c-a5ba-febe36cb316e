/**
 * GIS数据导入系统 - 任务管理功能
 * 提供任务列表查看、筛选、详情查看和操作功能
 */

class TaskManager {
    constructor() {
        this.tasks = [];
        this.filteredTasks = [];
        this.currentTask = null;
        this.refreshInterval = null;
        
        this.initializeElements();
        this.bindEvents();
        this.loadTasks();
        this.startAutoRefresh();
    }

    initializeElements() {
        // 筛选元素
        this.statusFilter = document.getElementById('statusFilter');
        this.formatFilter = document.getElementById('formatFilter');
        this.sortSelect = document.getElementById('sortSelect');
        this.searchInput = document.getElementById('searchInput');
        this.refreshBtn = document.getElementById('refreshBtn');
        this.floatingRefreshBtn = document.getElementById('floatingRefreshBtn');
        
        // 显示元素
        this.taskList = document.getElementById('taskList');
        this.emptyState = document.getElementById('emptyState');
        this.loadingState = document.getElementById('loadingState');
        
        // 统计元素
        this.totalTasks = document.getElementById('totalTasks');
        this.completedTasks = document.getElementById('completedTasks');
        this.processingTasks = document.getElementById('processingTasks');
        this.errorTasks = document.getElementById('errorTasks');
        this.pendingTasks = document.getElementById('pendingTasks');
        this.successRate = document.getElementById('successRate');
        
        // 模态框元素
        this.taskDetailModal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
        this.taskDetailContent = document.getElementById('taskDetailContent');
        this.validateTaskBtn = document.getElementById('validateTaskBtn');
        this.executeTaskBtn = document.getElementById('executeTaskBtn');
        this.downloadReportBtn = document.getElementById('downloadReportBtn');
        
        // Toast通知
        this.toast = new bootstrap.Toast(document.getElementById('toast'));
        this.toastMessage = document.getElementById('toastMessage');
    }

    bindEvents() {
        // 筛选和搜索事件
        this.statusFilter.addEventListener('change', this.handleFilter.bind(this));
        this.formatFilter.addEventListener('change', this.handleFilter.bind(this));
        this.sortSelect.addEventListener('change', this.handleFilter.bind(this));
        this.searchInput.addEventListener('input', this.handleSearch.bind(this));
        
        // 刷新按钮事件
        this.refreshBtn.addEventListener('click', this.loadTasks.bind(this));
        this.floatingRefreshBtn.addEventListener('click', this.loadTasks.bind(this));
        
        // 模态框按钮事件
        this.validateTaskBtn.addEventListener('click', this.handleValidateTask.bind(this));
        this.executeTaskBtn.addEventListener('click', this.handleExecuteTask.bind(this));
        this.downloadReportBtn.addEventListener('click', this.handleDownloadReport.bind(this));
    }

    // 加载任务列表
    async loadTasks() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/file-import/tasks');
            const result = await response.json();
            
            if (result.success && result.data) {
                // 按ID降序排列（最新的任务在前面）
                this.tasks = result.data.sort((a, b) => b.id - a.id);
                this.updateStatistics();
                this.applyFilters();
                this.hideLoading();
            } else {
                throw new Error(result.message || '加载任务列表失败');
            }
        } catch (error) {
            console.error('加载任务失败:', error);
            this.showToast('加载任务列表失败: ' + error.message, 'error');
            this.hideLoading();
        }
    }

    // 更新统计信息
    updateStatistics() {
        const stats = {
            total: this.tasks.length,
            completed: this.tasks.filter(t => t.dataStatus === 4).length,
            processing: this.tasks.filter(t => [1, 2, 3].includes(t.dataStatus)).length,
            error: this.tasks.filter(t => t.dataStatus === -1).length,
            pending: this.tasks.filter(t => t.dataStatus === 0).length
        };
        
        stats.successRate = stats.total > 0 ? ((stats.completed / stats.total) * 100).toFixed(1) : 0;
        
        this.totalTasks.textContent = stats.total;
        this.completedTasks.textContent = stats.completed;
        this.processingTasks.textContent = stats.processing;
        this.errorTasks.textContent = stats.error;
        this.pendingTasks.textContent = stats.pending;
        this.successRate.textContent = stats.successRate + '%';
    }

    // 应用筛选条件
    applyFilters() {
        let filtered = [...this.tasks];

        // 状态筛选
        if (this.statusFilter.value) {
            filtered = filtered.filter(task => task.dataStatus == this.statusFilter.value);
        }

        // 格式筛选
        if (this.formatFilter.value) {
            filtered = filtered.filter(task => task.importFormat == this.formatFilter.value);
        }

        // 搜索筛选
        const searchTerm = this.searchInput.value.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(task =>
                task.taskName.toLowerCase().includes(searchTerm) ||
                task.id.toString().includes(searchTerm)
            );
        }

        // 排序
        this.applySorting(filtered);

        this.filteredTasks = filtered;
        this.renderTasks();
    }

    // 应用排序
    applySorting(tasks) {
        const sortType = this.sortSelect.value;

        switch (sortType) {
            case 'id_desc':
                tasks.sort((a, b) => b.id - a.id);
                break;
            case 'id_asc':
                tasks.sort((a, b) => a.id - b.id);
                break;
            case 'name_asc':
                tasks.sort((a, b) => a.taskName.localeCompare(b.taskName));
                break;
            case 'name_desc':
                tasks.sort((a, b) => b.taskName.localeCompare(a.taskName));
                break;
            case 'status_asc':
                tasks.sort((a, b) => a.dataStatus - b.dataStatus);
                break;
            default:
                // 默认按ID降序
                tasks.sort((a, b) => b.id - a.id);
        }
    }

    handleFilter() {
        this.applyFilters();
    }

    handleSearch() {
        // 防抖处理
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.applyFilters();
        }, 300);
    }

    // 渲染任务列表
    renderTasks() {
        if (this.filteredTasks.length === 0) {
            this.taskList.innerHTML = '';
            this.emptyState.classList.remove('d-none');
            return;
        }
        
        this.emptyState.classList.add('d-none');
        
        this.taskList.innerHTML = this.filteredTasks.map(task => this.createTaskCard(task)).join('');
        
        // 绑定任务卡片事件
        this.bindTaskCardEvents();
    }

    // 创建任务卡片
    createTaskCard(task) {
        const statusInfo = this.getStatusInfo(task.dataStatus);
        const formatInfo = this.getFormatInfo(task.importFormat);
        const createdTime = new Date(task.createdAt).toLocaleString();
        
        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card task-card ${statusInfo.class}" data-task-id="${task.id}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">${task.taskName}</h6>
                        <span class="badge ${statusInfo.badgeClass} status-badge">${statusInfo.text}</span>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="bi bi-file-earmark"></i> ${formatInfo.text}
                                <span class="ms-2"><i class="bi bi-calendar"></i> ${createdTime}</span>
                            </small>
                        </div>
                        
                        ${task.processedCount ? `
                            <div class="task-metrics">
                                <div class="metric-item">
                                    <span>处理记录:</span>
                                    <strong>${task.processedCount || 0}</strong>
                                </div>
                                <div class="metric-item">
                                    <span>文件大小:</span>
                                    <strong>${this.formatFileSize(task.fileSize || 0)}</strong>
                                </div>
                            </div>
                        ` : ''}
                        
                        <div class="mt-3">
                            <button type="button" class="btn btn-sm btn-outline-primary task-detail-btn" data-task-id="${task.id}">
                                <i class="bi bi-eye"></i> 查看详情
                            </button>
                            ${this.getTaskActionButtons(task)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取状态信息
    getStatusInfo(status) {
        const statusMap = {
            0: { text: '未导入', class: 'status-pending', badgeClass: 'bg-warning' },
            1: { text: '验证中', class: 'status-validating', badgeClass: 'bg-info' },
            2: { text: '已验证', class: 'status-validated', badgeClass: 'bg-success' },
            3: { text: '导入中', class: 'status-importing', badgeClass: 'bg-primary' },
            4: { text: '已完成', class: 'status-completed', badgeClass: 'bg-success' },
            [-1]: { text: '失败', class: 'status-error', badgeClass: 'bg-danger' }
        };
        return statusMap[status] || { text: '未知', class: '', badgeClass: 'bg-secondary' };
    }

    // 获取格式信息
    getFormatInfo(format) {
        const formatMap = {
            1: { text: 'Excel文件', icon: 'file-earmark-excel' },
            2: { text: 'Shapefile', icon: 'file-earmark-zip' }
        };
        return formatMap[format] || { text: '未知格式', icon: 'file-earmark' };
    }

    // 获取任务操作按钮
    getTaskActionButtons(task) {
        const buttons = [];
        
        if (task.dataStatus === 0) {
            buttons.push(`
                <button type="button" class="btn btn-sm btn-success validate-btn ms-1" data-task-id="${task.id}">
                    <i class="bi bi-check-circle"></i> 验证
                </button>
            `);
        }
        
        if (task.dataStatus === 2) {
            buttons.push(`
                <button type="button" class="btn btn-sm btn-primary execute-btn ms-1" data-task-id="${task.id}">
                    <i class="bi bi-play-fill"></i> 导入
                </button>
            `);
        }
        
        return buttons.join('');
    }

    // 绑定任务卡片事件
    bindTaskCardEvents() {
        // 详情按钮
        document.querySelectorAll('.task-detail-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.closest('[data-task-id]').dataset.taskId;
                this.showTaskDetail(taskId);
            });
        });
        
        // 验证按钮
        document.querySelectorAll('.validate-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.closest('[data-task-id]').dataset.taskId;
                this.validateTask(taskId);
            });
        });
        
        // 执行按钮
        document.querySelectorAll('.execute-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.closest('[data-task-id]').dataset.taskId;
                this.executeTask(taskId);
            });
        });
    }

    // 显示任务详情
    async showTaskDetail(taskId) {
        try {
            const response = await fetch(`/api/file-import/tasks/${taskId}`);
            const result = await response.json();
            
            if (result.success && result.data) {
                this.currentTask = result.data;
                this.renderTaskDetail(result.data);
                this.taskDetailModal.show();
            } else {
                throw new Error(result.message || '获取任务详情失败');
            }
        } catch (error) {
            console.error('获取任务详情失败:', error);
            this.showToast('获取任务详情失败: ' + error.message, 'error');
        }
    }

    // 渲染任务详情
    renderTaskDetail(task) {
        const statusInfo = this.getStatusInfo(task.dataStatus);
        const formatInfo = this.getFormatInfo(task.importFormat);
        
        this.taskDetailContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>任务ID:</td><td>${task.id}</td></tr>
                        <tr><td>任务名称:</td><td>${task.taskName}</td></tr>
                        <tr><td>文件格式:</td><td>${formatInfo.text}</td></tr>
                        <tr><td>状态:</td><td><span class="badge ${statusInfo.badgeClass}">${statusInfo.text}</span></td></tr>
                        <tr><td>创建时间:</td><td>${new Date(task.createdAt).toLocaleString()}</td></tr>
                        <tr><td>创建者:</td><td>${task.createdBy || '未知'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>处理信息</h6>
                    <table class="table table-sm">
                        <tr><td>文件大小:</td><td>${this.formatFileSize(task.fileSize || 0)}</td></tr>
                        <tr><td>处理记录数:</td><td>${task.processedCount || 0}</td></tr>
                        <tr><td>模板ID:</td><td>${task.templateId || '未指定'}</td></tr>
                        <tr><td>开始时间:</td><td>${task.startTime ? new Date(task.startTime).toLocaleString() : '未开始'}</td></tr>
                        <tr><td>结束时间:</td><td>${task.endTime ? new Date(task.endTime).toLocaleString() : '未结束'}</td></tr>
                    </table>
                </div>
            </div>
        `;
        
        // 更新模态框按钮状态
        this.updateModalButtons(task);
    }

    // 更新模态框按钮状态
    updateModalButtons(task) {
        // 隐藏所有按钮
        this.validateTaskBtn.style.display = 'none';
        this.executeTaskBtn.style.display = 'none';
        this.downloadReportBtn.style.display = 'none';
        
        // 根据状态显示相应按钮
        if (task.dataStatus === 0) {
            this.validateTaskBtn.style.display = 'inline-block';
        }
        
        if (task.dataStatus === 2) {
            this.executeTaskBtn.style.display = 'inline-block';
            this.downloadReportBtn.style.display = 'inline-block';
        }
        
        if (task.dataStatus === 4 || task.dataStatus === -1) {
            this.downloadReportBtn.style.display = 'inline-block';
        }
    }

    // 验证任务
    async validateTask(taskId) {
        try {
            this.showToast('开始验证数据...', 'info');
            
            const response = await fetch(`/api/file-import/tasks/${taskId}/validate`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast('数据验证完成', 'success');
                this.loadTasks(); // 刷新任务列表
            } else {
                throw new Error(result.message || '验证失败');
            }
        } catch (error) {
            console.error('验证失败:', error);
            this.showToast('验证失败: ' + error.message, 'error');
        }
    }

    // 执行任务
    async executeTask(taskId) {
        try {
            this.showToast('开始执行导入...', 'info');
            
            const response = await fetch(`/api/file-import/tasks/${taskId}/execute`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast('导入完成', 'success');
                this.loadTasks(); // 刷新任务列表
            } else {
                throw new Error(result.message || '导入失败');
            }
        } catch (error) {
            console.error('导入失败:', error);
            this.showToast('导入失败: ' + error.message, 'error');
        }
    }

    // 模态框按钮处理
    handleValidateTask() {
        if (this.currentTask) {
            this.validateTask(this.currentTask.id);
            this.taskDetailModal.hide();
        }
    }

    handleExecuteTask() {
        if (this.currentTask) {
            this.executeTask(this.currentTask.id);
            this.taskDetailModal.hide();
        }
    }

    async handleDownloadReport() {
        if (!this.currentTask) return;
        
        try {
            const response = await fetch(`/api/file-import/tasks/${this.currentTask.id}/error-report`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `错误报告_任务${this.currentTask.id}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showToast('错误报告下载成功', 'success');
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('下载错误报告失败:', error);
            this.showToast('下载错误报告失败: ' + error.message, 'error');
        }
    }

    // 自动刷新
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadTasks();
        }, 30000); // 30秒刷新一次
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // UI 辅助方法
    showLoading() {
        this.loadingState.classList.remove('d-none');
        this.taskList.innerHTML = '';
        this.emptyState.classList.add('d-none');
    }

    hideLoading() {
        this.loadingState.classList.add('d-none');
    }

    showToast(message, type = 'info') {
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        this.toastMessage.innerHTML = `
            <i class="bi bi-${iconMap[type]} me-2"></i>
            ${message}
        `;
        
        this.toast.show();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 初始化任务管理器
document.addEventListener('DOMContentLoaded', () => {
    new TaskManager();
});
