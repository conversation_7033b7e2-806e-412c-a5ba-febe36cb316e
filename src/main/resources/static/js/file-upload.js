/**
 * GIS数据导入系统 - 文件上传功能
 * 支持Excel和Shapefile文件的上传、验证和导入
 */

class FileUploadManager {
    constructor() {
        this.currentTask = null;
        this.validationTimer = null;
        this.templates = [];
        
        this.initializeElements();
        this.bindEvents();
        this.loadTemplates();
    }

    initializeElements() {
        // 核心元素
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.fileInfo = document.getElementById('fileInfo');
        this.uploadProgress = document.getElementById('uploadProgress');
        this.taskStatus = document.getElementById('taskStatus');
        this.validationResults = document.getElementById('validationResults');
        
        // 表单元素
        this.templateSelect = document.getElementById('templateSelect');
        this.taskNameInput = document.getElementById('taskName');
        
        // 按钮元素
        this.uploadBtn = document.getElementById('uploadBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.executeImportBtn = document.getElementById('executeImportBtn');
        this.downloadErrorReportBtn = document.getElementById('downloadErrorReportBtn');
        
        // 显示元素
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        this.validationMetrics = document.getElementById('validationMetrics');
        
        // Toast通知
        this.toast = new bootstrap.Toast(document.getElementById('toast'));
        this.toastMessage = document.getElementById('toastMessage');
    }

    bindEvents() {
        // 文件上传区域事件
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        
        // 文件选择事件
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        
        // 按钮事件
        this.uploadBtn.addEventListener('click', this.handleUpload.bind(this));
        this.resetBtn.addEventListener('click', this.handleReset.bind(this));
        this.executeImportBtn.addEventListener('click', this.handleExecuteImport.bind(this));
        this.downloadErrorReportBtn.addEventListener('click', this.handleDownloadErrorReport.bind(this));
        
        // 模板选择事件
        this.templateSelect.addEventListener('change', this.handleTemplateChange.bind(this));
        
        // 移除文件事件
        document.getElementById('removeFile').addEventListener('click', this.handleRemoveFile.bind(this));
    }

    // 加载模板列表
    async loadTemplates() {
        try {
            const response = await fetch('/api/template-shapefile/templates');
            const result = await response.json();
            
            if (result.success && result.data) {
                this.templates = result.data;
                this.populateTemplateSelect();
            } else {
                this.showToast('加载模板列表失败', 'error');
            }
        } catch (error) {
            console.error('加载模板失败:', error);
            this.showToast('加载模板列表失败: ' + error.message, 'error');
        }
    }

    populateTemplateSelect() {
        this.templateSelect.innerHTML = '<option value="">请选择模板...</option>';
        
        this.templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template.id;
            option.textContent = `${template.nameZh} (${template.templateType || 'Unknown'})`;
            this.templateSelect.appendChild(option);
        });
    }

    // 拖拽事件处理
    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelection(files[0]);
        }
    }

    // 文件选择处理
    handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            this.handleFileSelection(files[0]);
        }
    }

    handleFileSelection(file) {
        // 验证文件类型
        const allowedTypes = ['.xlsx', '.xls', '.zip'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            this.showToast('不支持的文件格式，请选择 .xlsx, .xls 或 .zip 文件', 'error');
            return;
        }

        // 验证文件大小 (500MB)
        const maxSize = 500 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showToast('文件大小超过限制 (500MB)', 'error');
            return;
        }

        // 显示文件信息
        this.fileName.textContent = file.name;
        this.fileSize.textContent = this.formatFileSize(file.size);
        this.fileInfo.classList.remove('d-none');
        
        // 启用上传按钮
        this.updateUploadButton();
        
        // 自动生成任务名称
        if (!this.taskNameInput.value) {
            const baseName = file.name.replace(/\.[^/.]+$/, "");
            this.taskNameInput.value = `导入任务_${baseName}_${new Date().toLocaleString()}`;
        }
    }

    handleRemoveFile() {
        this.fileInput.value = '';
        this.fileInfo.classList.add('d-none');
        this.updateUploadButton();
    }

    handleTemplateChange() {
        this.updateUploadButton();
    }

    updateUploadButton() {
        const hasFile = this.fileInput.files.length > 0;
        const hasTemplate = this.templateSelect.value !== '';
        this.uploadBtn.disabled = !(hasFile && hasTemplate);
    }

    // 文件上传处理
    async handleUpload() {
        if (!this.fileInput.files.length || !this.templateSelect.value) {
            this.showToast('请选择文件和模板', 'error');
            return;
        }

        const file = this.fileInput.files[0];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('templateId', this.templateSelect.value);
        formData.append('taskName', this.taskNameInput.value || `导入任务_${file.name}`);
        formData.append('createdBy', 'web_user');

        try {
            this.showUploadProgress();
            this.uploadBtn.disabled = true;

            const response = await fetch('/api/file-import/tasks', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentTask = result.data;
                this.showToast('任务创建成功，开始验证数据...', 'success');
                this.updateTaskStatus('created');
                
                // 自动开始验证
                setTimeout(() => this.startValidation(), 1000);
            } else {
                throw new Error(result.message || '任务创建失败');
            }
        } catch (error) {
            console.error('上传失败:', error);
            this.showToast('上传失败: ' + error.message, 'error');
            this.hideUploadProgress();
            this.uploadBtn.disabled = false;
        }
    }

    // 开始数据验证
    async startValidation() {
        if (!this.currentTask) return;

        try {
            this.updateTaskStatus('validating');
            
            const response = await fetch(`/api/file-import/tasks/${this.currentTask.id}/validate`, {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.showValidationResults(result.data);
                this.updateTaskStatus('validated');
            } else {
                throw new Error(result.message || '验证失败');
            }
        } catch (error) {
            console.error('验证失败:', error);
            this.showToast('验证失败: ' + error.message, 'error');
            this.updateTaskStatus('error');
        }
    }

    // 显示验证结果
    showValidationResults(validationResult) {
        this.hideUploadProgress();
        
        // 构建验证指标
        const metrics = [
            { label: '总记录数', value: validationResult.totalRecords, color: 'primary' },
            { label: '有效记录', value: validationResult.validRecords, color: 'success' },
            { label: '错误记录', value: validationResult.errorRecords, color: 'danger' },
            { label: '错误率', value: `${validationResult.errorRate.toFixed(2)}%`, color: 'warning' }
        ];

        this.validationMetrics.innerHTML = metrics.map(metric => `
            <div class="col-md-3">
                <div class="metric-card bg-${metric.color} bg-opacity-10 border border-${metric.color} border-opacity-25">
                    <div class="metric-value text-${metric.color}">${metric.value}</div>
                    <div class="metric-label">${metric.label}</div>
                </div>
            </div>
        `).join('');

        // 显示验证结果区域
        this.validationResults.classList.remove('d-none');

        // 更新按钮状态
        this.executeImportBtn.disabled = !validationResult.passed;
        this.downloadErrorReportBtn.disabled = !validationResult.errorFilePath;

        // 显示验证摘要
        const statusClass = validationResult.passed ? 'success' : 'error';
        const statusText = validationResult.passed ? '验证通过，可以执行导入' : '验证未通过，请检查错误报告';
        this.showToast(statusText, statusClass);
    }

    // 执行导入
    async handleExecuteImport() {
        if (!this.currentTask) return;

        try {
            this.executeImportBtn.disabled = true;
            this.updateTaskStatus('importing');

            const response = await fetch(`/api/file-import/tasks/${this.currentTask.id}/execute`, {
                method: 'POST'
            });

            const result = await response.json();

            if (result.success) {
                this.showToast('导入完成！', 'success');
                this.updateTaskStatus('completed');
            } else {
                throw new Error(result.message || '导入失败');
            }
        } catch (error) {
            console.error('导入失败:', error);
            this.showToast('导入失败: ' + error.message, 'error');
            this.updateTaskStatus('error');
        } finally {
            this.executeImportBtn.disabled = false;
        }
    }

    // 下载错误报告
    async handleDownloadErrorReport() {
        if (!this.currentTask) return;

        try {
            const response = await fetch(`/api/file-import/tasks/${this.currentTask.id}/error-report`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `错误报告_任务${this.currentTask.id}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showToast('错误报告下载成功', 'success');
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('下载错误报告失败:', error);
            this.showToast('下载错误报告失败: ' + error.message, 'error');
        }
    }

    // 重置表单
    handleReset() {
        this.fileInput.value = '';
        this.taskNameInput.value = '';
        this.templateSelect.value = '';
        this.fileInfo.classList.add('d-none');
        this.validationResults.classList.add('d-none');
        this.hideUploadProgress();
        this.updateUploadButton();
        this.currentTask = null;
        this.updateTaskStatus('idle');
    }

    // UI 更新方法
    showUploadProgress() {
        this.uploadProgress.classList.remove('d-none');
        this.progressBar.style.width = '100%';
        this.progressText.textContent = '上传中...';
    }

    hideUploadProgress() {
        this.uploadProgress.classList.add('d-none');
        this.progressBar.style.width = '0%';
        this.progressText.textContent = '0%';
    }

    updateTaskStatus(status) {
        const statusConfig = {
            idle: { icon: 'info-circle', text: '请先上传文件创建任务', class: '' },
            created: { icon: 'check-circle', text: '任务创建成功', class: 'status-success' },
            validating: { icon: 'arrow-repeat', text: '正在验证数据...', class: 'status-validating' },
            validated: { icon: 'check-circle', text: '数据验证完成', class: 'status-success' },
            importing: { icon: 'arrow-repeat', text: '正在导入数据...', class: 'status-validating' },
            completed: { icon: 'check-circle', text: '导入完成', class: 'status-success' },
            error: { icon: 'exclamation-triangle', text: '处理失败', class: 'status-error' }
        };

        const config = statusConfig[status] || statusConfig.idle;
        
        this.taskStatus.innerHTML = `
            <div class="task-status ${config.class}">
                <i class="bi bi-${config.icon}"></i>
                <span class="ms-2">${config.text}</span>
                ${this.currentTask ? `<small class="d-block mt-1">任务ID: ${this.currentTask.id}</small>` : ''}
            </div>
        `;
    }

    showToast(message, type = 'info') {
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        this.toastMessage.innerHTML = `
            <i class="bi bi-${iconMap[type]} me-2"></i>
            ${message}
        `;
        
        this.toast.show();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 初始化文件上传管理器
document.addEventListener('DOMContentLoaded', () => {
    new FileUploadManager();
});
