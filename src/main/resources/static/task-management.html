<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GIS数据导入系统 - 任务管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .task-card {
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        .task-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .task-card.status-pending { border-left-color: #ffc107; }
        .task-card.status-validating { border-left-color: #0dcaf0; }
        .task-card.status-validated { border-left-color: #198754; }
        .task-card.status-importing { border-left-color: #0d6efd; }
        .task-card.status-completed { border-left-color: #198754; }
        .task-card.status-error { border-left-color: #dc3545; }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .task-metrics {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .metric-item:last-child {
            margin-bottom: 0;
        }

        .filter-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-list-task"></i> 任务管理</h2>
                        <p class="text-muted">查看和管理所有数据导入任务</p>
                    </div>
                    <div>
                        <a href="file-upload.html" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> 新建任务
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-3">
                    <label for="statusFilter" class="form-label">任务状态</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="0">未导入</option>
                        <option value="1">验证中</option>
                        <option value="2">已验证</option>
                        <option value="3">导入中</option>
                        <option value="4">已完成</option>
                        <option value="-1">失败</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="formatFilter" class="form-label">文件格式</label>
                    <select class="form-select" id="formatFilter">
                        <option value="">全部格式</option>
                        <option value="1">Excel</option>
                        <option value="2">Shapefile</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="sortSelect" class="form-label">排序方式</label>
                    <select class="form-select" id="sortSelect">
                        <option value="id_desc">最新任务优先</option>
                        <option value="id_asc">最早任务优先</option>
                        <option value="name_asc">任务名称 A-Z</option>
                        <option value="name_desc">任务名称 Z-A</option>
                        <option value="status_asc">状态排序</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchInput" class="form-label">搜索任务</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="输入任务名称或ID">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="button" class="btn btn-outline-primary" id="refreshBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务统计 -->
        <div class="row mb-4" id="taskStats">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-primary" id="totalTasks">0</h5>
                        <p class="card-text small">总任务数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success" id="completedTasks">0</h5>
                        <p class="card-text small">已完成</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info" id="processingTasks">0</h5>
                        <p class="card-text small">处理中</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-danger" id="errorTasks">0</h5>
                        <p class="card-text small">失败</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-warning" id="pendingTasks">0</h5>
                        <p class="card-text small">待处理</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-secondary" id="successRate">0%</h5>
                        <p class="card-text small">成功率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="row" id="taskList">
            <!-- 动态生成任务卡片 -->
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="empty-state d-none">
            <i class="bi bi-inbox" style="font-size: 4rem;"></i>
            <h4 class="mt-3">暂无任务</h4>
            <p>还没有创建任何导入任务</p>
            <a href="file-upload.html" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建第一个任务
            </a>
        </div>

        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3">正在加载任务列表...</p>
        </div>
    </div>

    <!-- 任务详情模态框 -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="taskDetailContent">
                    <!-- 动态生成任务详情 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="validateTaskBtn" style="display: none;">
                        <i class="bi bi-check-circle"></i> 验证数据
                    </button>
                    <button type="button" class="btn btn-success" id="executeTaskBtn" style="display: none;">
                        <i class="bi bi-play-fill"></i> 执行导入
                    </button>
                    <button type="button" class="btn btn-warning" id="downloadReportBtn" style="display: none;">
                        <i class="bi bi-download"></i> 下载报告
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle me-2"></i>
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- 动态消息内容 -->
            </div>
        </div>
    </div>

    <!-- 浮动刷新按钮 -->
    <button type="button" class="btn btn-primary btn-lg refresh-btn" id="floatingRefreshBtn" title="刷新任务列表">
        <i class="bi bi-arrow-clockwise"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/task-management.js"></script>
</body>
</html>
