package com.zjxy.gisimportservice.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;

import java.util.List;

/**
 * List<String> 类型的JSON处理器
 */
public class ListStringToJsonHandler extends AbstractJsonTypeHandler<List<String>> {
    @Override
    protected List<String> parse(String json) {
        return JSON.parseObject(json, new TypeReference<List<String>>(){});
    }

    @Override
    protected String toJson(List<String> obj) {
        return JSON.toJSONString(obj);
    }
}
