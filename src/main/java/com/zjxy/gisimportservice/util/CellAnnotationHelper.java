package com.zjxy.gisimportservice.util;

import com.zjxy.gisimportservice.listener.ExcelDataListener;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;

/**
 * Excel单元格标注辅助工具类
 * 用于为验证错误的单元格添加颜色标注和注释
 * 
 * <AUTHOR> Agent
 * @since 2025-08-01
 */
public class CellAnnotationHelper {

    /**
     * 获取错误对应的背景色
     * 
     * @param errorType 错误类型
     * @return 对应的颜色索引
     */
    public static short getErrorBackgroundColor(String errorType) {
        switch (errorType) {
            // 严重错误 - 红色背景
            case "ENUM_VALUE_INVALID":
            case "ONLY_VALUE_INVALID":
            case "REQUIRED_FIELD":
            case "VALIDATION_EXCEPTION":
                return IndexedColors.RED.getIndex();
            
            // 警告错误 - 黄色背景
            case "SCALE_VALUE_TOO_SMALL":
            case "SCALE_VALUE_TOO_LARGE":
            case "DATA_TYPE_MISMATCH":
            case "SCALE_VALUE_NOT_NUMBER":
                return IndexedColors.YELLOW.getIndex();
            
            // 其他错误 - 橙色背景
            default:
                return IndexedColors.LIGHT_ORANGE.getIndex();
        }
    }

    /**
     * 获取错误对应的字体颜色
     * 
     * @param errorType 错误类型
     * @return 对应的字体颜色索引
     */
    public static short getErrorFontColor(String errorType) {
        switch (errorType) {
            // 严重错误 - 白色字体
            case "ENUM_VALUE_INVALID":
            case "ONLY_VALUE_INVALID":
            case "REQUIRED_FIELD":
            case "VALIDATION_EXCEPTION":
                return IndexedColors.WHITE.getIndex();
            
            // 警告和其他错误 - 黑色字体
            default:
                return IndexedColors.BLACK.getIndex();
        }
    }

    /**
     * 创建错误单元格样式
     * 
     * @param workbook Excel工作簿
     * @param errorType 错误类型
     * @return 配置好的单元格样式
     */
    public static CellStyle createErrorCellStyle(Workbook workbook, String errorType) {
        CellStyle errorStyle = workbook.createCellStyle();
        
        // 设置背景色
        errorStyle.setFillForegroundColor(getErrorBackgroundColor(errorType));
        errorStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 创建字体
        Font font = workbook.createFont();
        font.setColor(getErrorFontColor(errorType));
        font.setBold(true); // 错误字段使用粗体
        errorStyle.setFont(font);
        
        // 设置边框
        errorStyle.setBorderTop(BorderStyle.THIN);
        errorStyle.setBorderBottom(BorderStyle.THIN);
        errorStyle.setBorderLeft(BorderStyle.THIN);
        errorStyle.setBorderRight(BorderStyle.THIN);
        
        return errorStyle;
    }

    /**
     * 格式化错误注释文本
     * 
     * @param error 详细验证错误
     * @return 格式化后的注释文本
     */
    public static String formatErrorComment(ExcelDataListener.DetailedValidationError error) {
        StringBuilder comment = new StringBuilder();
        
        // 基本错误信息
        comment.append(String.format("第%d行 %s", 
                error.getRowNumber(), 
                error.getFieldValue() != null ? error.getFieldValue().toString() : "空值"));
        
        // 错误描述
        comment.append("\n\n错误描述：");
        comment.append(error.getErrorMessage());
        
        // 错误级别
        comment.append("\n\n错误级别：");
        comment.append(getErrorLevelDescription(error.getErrorType()));
        
        // 修复建议
        String suggestion = getFixSuggestion(error);
        if (suggestion != null && !suggestion.trim().isEmpty()) {
            comment.append("\n\n修复建议：");
            comment.append(suggestion);
        }
        
        return comment.toString();
    }

    /**
     * 获取错误级别描述
     * 
     * @param errorType 错误类型
     * @return 错误级别描述
     */
    private static String getErrorLevelDescription(String errorType) {
        switch (errorType) {
            case "ENUM_VALUE_INVALID":
            case "ONLY_VALUE_INVALID":
            case "REQUIRED_FIELD":
            case "VALIDATION_EXCEPTION":
                return "严重";
            case "SCALE_VALUE_TOO_SMALL":
            case "SCALE_VALUE_TOO_LARGE":
            case "DATA_TYPE_MISMATCH":
            case "SCALE_VALUE_NOT_NUMBER":
                return "警告";
            default:
                return "提示";
        }
    }

    /**
     * 获取修复建议
     * 
     * @param error 详细验证错误
     * @return 修复建议文本
     */
    private static String getFixSuggestion(ExcelDataListener.DetailedValidationError error) {
        switch (error.getErrorType()) {
            case "ENUM_VALUE_INVALID":
                return "请选择允许的枚举值";
            case "ONLY_VALUE_INVALID":
                return "该字段只允许特定值";
            case "REQUIRED_FIELD":
                return "请填写必填字段";
            case "SCALE_VALUE_TOO_SMALL":
                return "请输入更大的数值";
            case "SCALE_VALUE_TOO_LARGE":
                return "请输入更小的数值";
            case "DATA_TYPE_MISMATCH":
                return "请检查数据类型是否正确";
            case "SCALE_VALUE_NOT_NUMBER":
                return "请输入有效的数值";
            default:
                return "请检查数据格式";
        }
    }

    /**
     * 添加错误注释到单元格
     * 
     * @param cell 目标单元格
     * @param error 详细验证错误
     * @param drawing 绘图对象
     */
    public static void addErrorComment(Cell cell, ExcelDataListener.DetailedValidationError error, Drawing<?> drawing) {
        // 创建注释
        ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, 
                cell.getColumnIndex(), cell.getRowIndex(), 
                cell.getColumnIndex() + 3, cell.getRowIndex() + 5);
        
        Comment comment = drawing.createCellComment(anchor);
        
        // 设置注释文本
        String commentText = formatErrorComment(error);
        comment.setString(new XSSFRichTextString(commentText));
        
        // 设置注释作者
        comment.setAuthor("数据验证系统");
        
        // 将注释附加到单元格
        cell.setCellComment(comment);
    }

    /**
     * 获取错误类型的中文描述
     * 
     * @param errorType 错误类型
     * @return 中文描述
     */
    public static String getErrorTypeDescription(String errorType) {
        switch (errorType) {
            case "ENUM_VALUE_INVALID":
                return "枚举值错误";
            case "ONLY_VALUE_INVALID":
                return "唯一值错误";
            case "REQUIRED_FIELD":
                return "必填字段为空";
            case "SCALE_VALUE_TOO_SMALL":
                return "数值过小";
            case "SCALE_VALUE_TOO_LARGE":
                return "数值过大";
            case "DATA_TYPE_MISMATCH":
                return "数据类型不匹配";
            case "SCALE_VALUE_NOT_NUMBER":
                return "非有效数值";
            case "VALIDATION_EXCEPTION":
                return "验证异常";
            default:
                return "未知错误";
        }
    }
}
