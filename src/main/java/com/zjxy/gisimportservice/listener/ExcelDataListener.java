package com.zjxy.gisimportservice.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.Impl.CoordinateTransformService;
import com.zjxy.gisimportservice.service.InsertService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel数据监听器
 *
 * 基于EasyExcel的数据监听器，用于处理Excel数据的读取、验证、转换和批量插入
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
public class ExcelDataListener extends AnalysisEventListener<Map<Integer, Object>> {

    /**
     * 模板配置
     */
    private final GisManageTemplate template;

    /**
     * 数据验证服务（合并了CommonValidationService功能）
     */
    private final DataValidationService validationService;

    /**
     * 坐标转换服务
     */
    private final CoordinateTransformService coordinateService;

    /**
     * 批量插入服务
     */
    private final InsertService batchInsertService;

    /**
     * 当前批次数据
     */
    private List<GeoFeatureEntity> batch = new ArrayList<>();

    /**
     * 批次大小
     */
    private final int batchSize;

    /**
     * 处理目标（import/valid/export）
     */
    private final String target;

    /**
     * 验证模式标识：true表示只验证不插入数据库
     */
    private boolean validationOnly = false;

    /**
     * 跳过验证标识：true表示跳过验证直接插入
     */
    private boolean skipValidation = false;

    /**
     * 统计信息
     */
    @Getter
    private int totalRecords = 0;
    @Getter
    private int successRecords = 0;
    @Getter
    private int errorRecords = 0;
    private int currentBatchNumber = 1;

    /**
     * 详细错误记录（包含行号和原始数据）
     */
    @Getter
    private final List<DetailedValidationError> detailedErrors = new ArrayList<>();

    /**
     * 获取错误列表（兼容性方法）
     * 为了保持向后兼容，将DetailedValidationError转换为简单的ValidationError
     */
    public List<ValidationResult.ValidationError> getErrors() {
        List<ValidationResult.ValidationError> simpleErrors = new ArrayList<>();

        for (DetailedValidationError detailedError : detailedErrors) {
            ValidationResult.ValidationError error = new ValidationResult.ValidationError();
            error.setRecordIndex(detailedError.getRowNumber());
            error.setErrorMessage(detailedError.getErrorMessage());
            error.setErrorType(ValidationResult.ErrorType.valueOf(detailedError.getErrorType()));
            error.setFeatureId(detailedError.getFieldName());
            simpleErrors.add(error);
        }

        return simpleErrors;
    }

    // Getter方法
    /**
     * 是否发生错误
     */
    @Getter
    private boolean hasError = false;

    /**
     * 表头映射：列名 -> 列索引
     */
    private final Map<String, Integer> headerMapping = new HashMap<>();

    /**
     * 是否已读取表头
     */
    private boolean headerRead = false;

    // ========== 性能优化：预处理缓存 ==========

    /**
     * 预处理的字段映射缓存：fieldName -> FieldMappingCache
     */
    private final Map<String, FieldMappingCache> fieldMappingCache = new HashMap<>();

    /**
     * 预处理的坐标映射缓存
     */
    private CoordinateMappingCache coordinateMappingCache;

    /**
     * 字段映射缓存结构（基于列名映射）
     */
    private static class FieldMappingCache {
        String fieldName;
        String columnName;
        String fieldType;
        boolean checked;
        Integer columnIndex; // 预解析的列索引
        String mappingSource; // 映射来源描述

        FieldMappingCache(String fieldName, String columnName, String fieldType, Boolean checked) {
            this.fieldName = fieldName;
            this.columnName = columnName;
            this.fieldType = fieldType;
            this.checked = checked != null ? checked : true;
        }
    }

    /**
     * 坐标映射缓存结构
     */
    private static class CoordinateMappingCache {
        // 线坐标映射
        Integer xColumnIndex;
        Integer yColumnIndex;
        Integer x1ColumnIndex;
        Integer y1ColumnIndex;

        // 点坐标映射
        Integer pointXColumnIndex;
        Integer pointYColumnIndex;

        // 是否有效
        boolean hasValidLineMapping;
        boolean hasValidPointMapping;
    }

    /**
     * 存储上一行的数据，用于合并单元格处理
     */
    private Map<Integer, Object> previousRowData = new HashMap<>();

    /**
     * 构造函数
     */
    public ExcelDataListener(GisManageTemplate template,
                           DataValidationService validationService,
                           CoordinateTransformService coordinateService,
                           InsertService batchInsertService,
                           int batchSize,
                           String target) {
        this.template = template;
        this.validationService = validationService;
        this.coordinateService = coordinateService;
        this.batchInsertService = batchInsertService;
        this.batchSize = batchSize;
        this.target = target;
    }

    /**
     * 简单的合并单元格值填充
     * 策略：如果某列为空，使用上一行同列的值填充（适用于纵向合并的单元格）
     */
    private Map<Integer, Object> fillMergedCellValues(Map<Integer, Object> rowData) {
        if (rowData == null || rowData.isEmpty()) {
            return rowData;
        }

        Map<Integer, Object> processedData = new HashMap<>(rowData);
        boolean hasChanges = false;

        // 检查每一列，如果为空则尝试从上一行获取值
        for (Map.Entry<Integer, Object> entry : rowData.entrySet()) {
            Integer colIndex = entry.getKey();
            Object cellValue = entry.getValue();

            // 如果当前单元格为空或null
            if (cellValue == null || (cellValue instanceof String && ((String) cellValue).trim().isEmpty())) {
                // 尝试从上一行同列获取值
                Object previousValue = previousRowData.get(colIndex);
                if (previousValue != null && !previousValue.toString().trim().isEmpty()) {
                    processedData.put(colIndex, previousValue);
                    hasChanges = true;
                    log.debug("从上一行填充合并单元格值: 列{} = '{}'", colIndex, previousValue);
                }
            }
        }

        // 保存当前行数据供下一行使用
        previousRowData = new HashMap<>(processedData);

        if (hasChanges) {
            log.debug("合并单元格处理完成，从上一行填充了空值");
        }

        return processedData;
    }

    /**
     * 处理每一行数据
     */
    @Override
    public void invoke(Map<Integer, Object> data, AnalysisContext context) {
        long rowStartTime = System.currentTimeMillis(); // 性能监控：记录行处理开始时间
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        log.debug("=== 处理第 {} 行数据开始 ===", rowIndex);
        log.debug("原始数据: {}", data);
        log.debug("当前目标模式: {}", target);

        try {
            // ========== 修复后的表头处理逻辑 ==========
            int currentRowIndex = context.readRowHolder().getRowIndex();
            int rowNumber = currentRowIndex + 1;
            Integer thLine = template.getThLine();
            if (thLine == null || thLine <= 0) {
                thLine = 1; // 默认第1行是表头
            }

            log.debug("=== 行处理详情 ===");
            log.debug("当前行索引: {} (第{}行)", currentRowIndex, rowNumber);
            log.debug("th_line配置: {} (期望表头在第{}行)", thLine, thLine);
            log.debug("表头已读取: {}", headerRead);
            log.debug("前5列数据: [0={}, 1={}, 2={}, 3={}, 4={}]",
                    data.get(0), data.get(1), data.get(2), data.get(3), data.get(4));

            // 修复1：检查是否为表头行
            if (!headerRead && isHeaderRow(context)) {
                log.info("✅ 识别为表头行 (第{}行，索引{})，开始读取表头", rowNumber, currentRowIndex);
                readHeader(data);
                return;
            }

            // 修复2：如果表头未读取但当前不是表头行，报错
            if (!headerRead) {
                int expectedHeaderIndex = thLine - 1;
                log.error("❌ 严重错误：表头尚未读取！");
                log.error("期望表头位置: 第{}行 (索引{})", thLine, expectedHeaderIndex);
                log.error("当前处理位置: 第{}行 (索引{})", rowNumber, currentRowIndex);
                log.error("这表明Excel读取配置有问题或表头行被跳过");

                // 验证当前行数据是否像表头
                boolean looksLikeHeader = validateHeaderLikeData(data);
                if (looksLikeHeader) {
                    log.warn("当前行数据看起来像表头，可能是th_line配置错误");
                    log.warn("建议检查th_line配置，当前配置: {}", template.getThLine());
                } else {
                    log.error("当前行数据看起来是数据行，不是表头行");
                }

                throw new RuntimeException(String.format(
                    "Excel表头读取失败：期望在第%d行(索引%d)找到表头，但当前处理第%d行(索引%d)。" +
                    "请检查Excel文件结构和th_line配置。当前行前5列数据：[%s, %s, %s, %s, %s]",
                    thLine, expectedHeaderIndex, rowNumber, currentRowIndex,
                    data.get(0), data.get(1), data.get(2), data.get(3), data.get(4)));
            }

            // 验证模式下继续处理所有行，收集所有错误
            if ("valid".equals(target) && hasError) {
                log.debug("验证模式下继续处理第 {} 行，收集所有错误", rowIndex);
            }

            // 检查数据是否为空
            if (data == null || data.isEmpty()) {
                log.warn("第 {} 行数据为空，跳过处理", rowIndex);
                return;
            }

            totalRecords++;

            // 转换为GeoFeatureEntity
            try {
                GeoFeatureEntity entity = convertToGeoFeatureEntity(data, rowIndex);
                if (entity != null) {
                    batch.add(entity);
                    log.debug("第 {} 行数据转换成功，当前批次大小: {}/{}", rowIndex, batch.size(), batchSize);
                } else {
                    // 在验证模式下收集详细错误（使用统一的CommonValidationService）
                    if (validationOnly || "valid".equals(target)) {
                        validationService.collectDetailedError(rowIndex, "数据转换失败", "CONVERSION_FAILED",
                                data, null, null, detailedErrors);
                    }
                    log.error("第 {} 行数据转换失败，返回null", rowIndex);
                    errorRecords++;
                }
            } catch (Exception e) {
                // 在验证模式下收集详细错误（使用统一的CommonValidationService）
                if (validationOnly || "valid".equals(target)) {
                    validationService.collectDetailedError(rowIndex, "数据转换异常: " + e.getMessage(),
                            "CONVERSION_ERROR", data, null, null, detailedErrors);
                }
                log.error("第 {} 行数据转换异常: {}", rowIndex, e.getMessage());
                errorRecords++;
            }

            // 达到批次大小时处理批次
            if (batch.size() >= batchSize) {
                log.info("达到批次大小 {}，开始处理批次", batchSize);
                processBatch();
            }

        } catch (Exception e) {
            log.error("处理第 {} 行数据时发生错误: {}", rowIndex, e.getMessage(), e);
            errorRecords++;
            hasError = true;

            // 在验证模式下收集详细错误（使用统一的CommonValidationService）
            if (validationOnly || "valid".equals(target)) {
                Map<Integer, Object> rowData = new HashMap<>();
                rowData.put(0, "数据处理失败: " + e.getMessage());

                validationService.collectDetailedError(
                    context.readRowHolder().getRowIndex(),
                    "数据处理失败: " + e.getMessage(),
                    "DATA_PROCESSING_FAILED",
                    rowData, null, null, detailedErrors);
            }

            // 性能监控：记录跳过的行
            performanceStats.skippedRows++;
        } finally {
            // 性能监控：记录行处理时间
            long rowEndTime = System.currentTimeMillis();
            long rowProcessingTime = rowEndTime - rowStartTime;
            performanceStats.recordRowProcessing(rowIndex, rowProcessingTime);

            if (rowProcessingTime > 50) { // 记录耗时超过50ms的行
                log.warn("第{}行数据处理耗时较长: {}ms", rowIndex, rowProcessingTime);
            }
        }
    }

    /**
     * 所有数据解析完成后的处理
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余的批次数据
        if (!batch.isEmpty()) {
            processBatch();
        }

        log.info("Excel数据处理完成 - 总记录数: {}, 成功: {}, 错误: {}",
                totalRecords, successRecords, errorRecords);

        // 输出性能统计
        performanceStats.logStats();
    }

    /**
     * 处理批次数据
     */
    private void processBatch() {
        if (batch.isEmpty()) {
            log.warn("批次数据为空，跳过处理");
            return;
        }

        log.info("=== 开始处理第 {} 批数据，数据量: {} ===", currentBatchNumber, batch.size());
        log.info("目标模式: {}", target);
        log.info("模板信息: ID={}, 表名={}, 数据库={}", template.getId(), template.getTableName(), template.getDataBase());

        try {
            // 验证已在数据转换阶段通过CommonValidationService完成，这里只需要检查是否有错误
            if (!skipValidation && !detailedErrors.isEmpty()) {
                hasError = true;
                log.info("当前批次验证发现 {} 个错误，已记录到详细错误列表", detailedErrors.size());

                // 在导入模式下，如果有错误则停止处理
                if (!"valid".equals(target) && !validationOnly) {
                    log.warn("导入模式下发现验证错误，停止处理");
                    return;
                }
            }

            // 2. 坐标转换
            log.info("步骤2: 检查坐标转换需求");
            if (validationOnly || "valid".equals(target)) {
                log.info("验证模式：跳过坐标转换，只进行数据格式验证");
            } else if (template.getIsZh() != null && template.getIsZh() && coordinateService != null) {
                log.info("开始坐标转换: {} -> {}", template.getOriginalCoordinateSystem(), template.getTargetCoordinateSystem());
                transformCoordinates(batch);
            } else {
                log.info("跳过坐标转换: isZh={}, coordinateService={}", template.getIsZh(), coordinateService != null);
            }

            // 4. 数据插入
            log.info("步骤4: 数据插入，目标模式: {}, 验证模式: {}", target, validationOnly);
            if (validationOnly) {
                // 纯验证模式：不执行数据库插入
                log.info("纯验证模式：跳过数据库插入，只进行验证");
            } else if ("import".equals(target)) {
                log.info("开始批量插入数据，数据量: {}", batch.size());
                insertBatch(batch);
                log.info("批量插入完成");
            } else if ("export".equals(target)) {
                // 导出模式的特殊处理
                log.info("处理导出模式");
                processExportBatch(batch);
            } else {
                log.info("目标模式为 {}，跳过数据插入", target);
            }

            successRecords += batch.size();
            log.info("=== 第 {} 批数据处理完成，成功记录数: {} ===", currentBatchNumber, batch.size());

        } catch (Exception e) {
            log.error("处理第 {} 批数据时发生错误: {}", currentBatchNumber, e.getMessage(), e);
            errorRecords += batch.size();
            hasError = true;
        } finally {
            // 清空当前批次
            batch.clear();
            currentBatchNumber++;
        }
    }

    /**
     * 将Excel行数据转换为GeoFeatureEntity
     */
    private GeoFeatureEntity convertToGeoFeatureEntity(Map<Integer, Object> rowData, int rowNumber) {
        log.debug("开始转换第 {} 行数据，原始数据: {}", rowNumber, rowData);

        GeoFeatureEntity entity = new GeoFeatureEntity();
        entity.setFeatureId("excel_row_" + rowNumber);
        entity.setCreatedAt(LocalDateTime.now());

        Map<String, Object> attributes = new HashMap<>();

        // 使用预处理的字段映射缓存（性能优化）
        if (log.isTraceEnabled()) {
            log.trace("使用预处理的字段映射缓存，缓存大小: {}", fieldMappingCache.size());
        }

        for (FieldMappingCache cache : fieldMappingCache.values()) {
            // 使用预解析的列索引
            Integer columnIndex = cache.columnIndex;
            String fieldName = cache.fieldName;
            String mappingSource = cache.mappingSource;

            if (log.isTraceEnabled()) {
                log.trace("处理字段映射缓存: fieldName={}, columnIndex={}, source={}",
                         fieldName, columnIndex, mappingSource);
            }

            // 检查数据是否存在
            if (!rowData.containsKey(columnIndex)) {
                log.debug("行数据中不存在列索引{} ({}，字段: {})，跳过", columnIndex, mappingSource, fieldName);
                continue;
            }

            // 获取并转换数据
            Object value = rowData.get(columnIndex);
            Object convertedValue = convertValue(value, cache.fieldType);

            // 使用数据库字段名作为key存储到attributes中
            attributes.put(fieldName, convertedValue);

            // 同时使用col_position格式的key存储，以便TemplateDataMapper能够找到
            String sourceFieldKey = "col_" + columnIndex;
            attributes.put(sourceFieldKey, convertedValue);

            log.debug("映射字段成功: {} ({}) = {} (原始值: {})", fieldName, mappingSource, convertedValue, value);
        }

        log.debug("转换后的属性: {}", attributes);

        // 处理坐标字段
        processCoordinateFields(rowData, attributes);

        entity.setRawAttributes(attributes);

        // 在验证模式下，使用统一验证服务进行验证
        if (validationOnly || "valid".equals(target)) {
            boolean isValid = validationService.validateDataWithTemplate(entity, template, rowNumber, detailedErrors);
            if (!isValid) {
                log.debug("第 {} 行数据验证失败", rowNumber);
            }
        }

        log.debug("第 {} 行数据转换完成，属性数量: {}", rowNumber, attributes.size());
        return entity;
    }

    /**
     * 处理坐标字段
     */
    private void processCoordinateFields(Map<Integer, Object> rowData, Map<String, Object> attributes) {
        // 检查是否为Excel模板且需要坐标处理
        if (!"excel".equalsIgnoreCase(template.getTemplateType())) {
            log.debug("非Excel模板，跳过坐标字段处理");
            return;
        }

        // 处理点坐标（type = 2）
        if (template.getType() != null && template.getType() == 2) {
            processPointCoordinates(rowData, attributes);
        }
        // 处理线坐标（type = 3）
        else if (template.getType() != null && template.getType() == 3) {
            processLineCoordinates(rowData, attributes);
        }
    }

    /**
     * 处理点坐标（type = 2）- 性能优化版本
     * 注意：此方法只进行坐标解析和几何生成，坐标转换在批次处理阶段进行
     */
    private void processPointCoordinates(Map<Integer, Object> rowData, Map<String, Object> attributes) {
        log.debug("开始处理点坐标数据");

        // 使用预处理的坐标映射缓存（性能优化）
        if (coordinateMappingCache == null || !coordinateMappingCache.hasValidPointMapping) {
            log.warn("点坐标映射缓存无效，跳过处理");
            return;
        }

        // 直接使用预解析的列索引（避免重复解析）
        Integer xColumn = coordinateMappingCache.pointXColumnIndex;
        Integer yColumn = coordinateMappingCache.pointYColumnIndex;

        log.debug("使用缓存的点坐标映射: X={}, Y={}", xColumn, yColumn);

        Object xValue = rowData.get(xColumn);
        Object yValue = rowData.get(yColumn);

        if (xValue == null || yValue == null) {
            log.debug("点坐标值为空: x={}, y={}", xValue, yValue);
            return;
        }

        try {
            double x = Double.parseDouble(xValue.toString());
            double y = Double.parseDouble(yValue.toString());

            // 验证坐标范围
            validateCoordinateRange(x, y);

            // 存储原始坐标
            attributes.put("x_coord", x);
            attributes.put("y_coord", y);

            // 生成WKT几何（使用原始坐标，转换在批次处理阶段进行）
            String wkt = String.format("POINT(%f %f)", x, y);
            attributes.put("geometry", wkt);

            log.debug("点坐标处理完成: ({}, {})", x, y);

        } catch (NumberFormatException e) {
            log.warn("点坐标值格式错误: x={}, y={}", xValue, yValue);
        } catch (Exception e) {
            log.error("处理点坐标时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理线坐标（type = 3）
     * 注意：此方法只进行坐标解析和几何生成，坐标转换在批次处理阶段进行
     */
    private void processLineCoordinates(Map<Integer, Object> rowData, Map<String, Object> attributes) {
        log.debug("开始处理线坐标数据");

        Map<String, Object> lineMap = template.getLineMap();
        if (lineMap == null) {
            log.debug("线坐标映射配置为空，跳过处理");
            return;
        }

        // 使用预处理的坐标映射缓存（性能优化）
        if (coordinateMappingCache == null || !coordinateMappingCache.hasValidLineMapping) {
            log.warn("线坐标映射缓存无效，跳过处理");
            return;
        }

        // 直接使用预解析的列索引（避免重复解析）
        Integer xColumn = coordinateMappingCache.xColumnIndex;
        Integer yColumn = coordinateMappingCache.yColumnIndex;
        Integer x1Column = coordinateMappingCache.x1ColumnIndex;
        Integer y1Column = coordinateMappingCache.y1ColumnIndex;

        log.debug("使用缓存的线坐标映射: 起点X={}, 起点Y={}, 终点X={}, 终点Y={}",
                xColumn, yColumn, x1Column, y1Column);

        // 获取坐标值
        Object xValue = rowData.get(xColumn);
        Object yValue = rowData.get(yColumn);
        Object x1Value = rowData.get(x1Column);
        Object y1Value = rowData.get(y1Column);

        if (xValue == null || yValue == null || x1Value == null || y1Value == null) {
            log.debug("线坐标值不完整: x={}, y={}, x1={}, y1={}", xValue, yValue, x1Value, y1Value);
            return;
        }

        try {
            double x = Double.parseDouble(xValue.toString());
            double y = Double.parseDouble(yValue.toString());
            double x1 = Double.parseDouble(x1Value.toString());
            double y1 = Double.parseDouble(y1Value.toString());

            // 验证坐标范围
            validateCoordinateRange(x, y);
            validateCoordinateRange(x1, y1);

            // 存储原始坐标
            attributes.put("x_coord", x);
            attributes.put("y_coord", y);
            attributes.put("x1_coord", x1);
            attributes.put("y1_coord", y1);

            // 生成WKT几何（使用原始坐标，转换在批次处理阶段进行）
            String wkt = String.format("LINESTRING(%f %f, %f %f)", x, y, x1, y1);
            attributes.put("geometry", wkt);

            log.debug("线坐标处理完成: ({}, {}) - ({}, {})", x, y, x1, y1);

        } catch (NumberFormatException e) {
            log.warn("线坐标值格式错误: x={}, y={}, x1={}, y1={}", xValue, yValue, x1Value, y1Value);
        } catch (Exception e) {
            log.error("处理线坐标时发生错误: {}", e.getMessage(), e);
        }
    }




    /**
     * 从WKT POINT字符串解析坐标
     * @param wkt WKT格式的POINT字符串，如 "POINT(120.5 30.2)"
     * @return 坐标数组 [x, y]，解析失败返回null
     */
    private double[] parsePointFromWkt(String wkt) {
        if (wkt == null || !wkt.toUpperCase().startsWith("POINT")) {
            return null;
        }

        try {
            // 提取坐标部分：POINT(120.5 30.2) -> 120.5 30.2
            String coordPart = wkt.substring(wkt.indexOf('(') + 1, wkt.lastIndexOf(')'));
            String[] coords = coordPart.trim().split("\\s+");

            if (coords.length >= 2) {
                double x = Double.parseDouble(coords[0]);
                double y = Double.parseDouble(coords[1]);
                return new double[]{x, y};
            }
        } catch (Exception e) {
            log.warn("解析POINT WKT失败: {}", wkt, e);
        }

        return null;
    }

    /**
     * 从WKT LINESTRING字符串解析坐标
     * @param wkt WKT格式的LINESTRING字符串，如 "LINESTRING(120.5 30.2, 121.0 30.5)"
     * @return 坐标数组 [[x1, y1], [x2, y2]]，解析失败返回null
     */
    private double[][] parseLineFromWkt(String wkt) {
        if (wkt == null || !wkt.toUpperCase().startsWith("LINESTRING")) {
            return null;
        }

        try {
            // 提取坐标部分：LINESTRING(120.5 30.2, 121.0 30.5) -> 120.5 30.2, 121.0 30.5
            String coordPart = wkt.substring(wkt.indexOf('(') + 1, wkt.lastIndexOf(')'));
            String[] pointStrings = coordPart.split(",");

            if (pointStrings.length >= 2) {
                double[][] coords = new double[pointStrings.length][];

                for (int i = 0; i < pointStrings.length; i++) {
                    String[] xy = pointStrings[i].trim().split("\\s+");
                    if (xy.length >= 2) {
                        coords[i] = new double[]{
                            Double.parseDouble(xy[0]),
                            Double.parseDouble(xy[1])
                        };
                    }
                }

                return coords;
            }
        } catch (Exception e) {
            log.warn("解析LINESTRING WKT失败: {}", wkt, e);
        }

        return null;
    }

    /**
     * 验证坐标范围
     */
    private void validateCoordinateRange(double x, double y) {
        String coordinateSystem = template.getOriginalCoordinateSystem();
        if (coordinateSystem != null) {
            // 根据坐标系验证坐标范围
            switch (coordinateSystem) {
                case "CGCS2000":
                case "BD09":
                    if (x < 119.5 || x > 121.4 || y < 26.9 || y > 28.7) {
                        throw new RuntimeException(String.format("坐标值(%.6f, %.6f)不符合%s坐标系的坐标范围", x, y, coordinateSystem));
                    }
                    break;
                case "WenZhou2000":
                case "WenZhouCity":
                    if (x < 384095.61 || x > 571661.61 || y < 2977083.96 || y > 3176239.57) {
                        throw new RuntimeException(String.format("坐标值(%.6f, %.6f)不符合%s坐标系的坐标范围", x, y, coordinateSystem));
                    }
                    break;
            }
        }
    }

    /**
     * 判断是否为表头行 - 修复版本
     */
    private boolean isHeaderRow(AnalysisContext context) {
        // 修复1：如果表头已读取，避免重复读取
        if (headerRead) {
            log.debug("表头已读取，跳过重复检测");
            return false;
        }

        // 修复2：严格验证th_line配置
        Integer thLine = template.getThLine();
        if (thLine == null || thLine <= 0) {
            thLine = 1; // 默认第1行是表头
            log.warn("th_line配置无效: {}, 使用默认值1", template.getThLine());
        }

        int currentRowIndex = context.readRowHolder().getRowIndex();
        int expectedHeaderIndex = thLine - 1; // 转换为0基索引

        boolean isHeader = currentRowIndex == expectedHeaderIndex;

        // 修复3：增强调试日志
        log.debug("=== 表头检测详情 ===");
        log.debug("当前行索引: {} (第{}行)", currentRowIndex, currentRowIndex + 1);
        log.debug("th_line配置: {} (期望表头在第{}行)", thLine, thLine);
        log.debug("期望表头索引: {} (0基索引)", expectedHeaderIndex);
        log.debug("是否表头行: {}", isHeader);
        log.debug("表头已读取: {}", headerRead);
        log.debug("=== 表头检测结束 ===");

        return isHeader;
    }

    /**
     * 验证数据是否像表头 - 新增方法
     */
    private boolean validateHeaderLikeData(Map<Integer, Object> data) {
        if (data == null || data.isEmpty()) {
            return false;
        }

        int textCount = 0;
        int numberCount = 0;
        int nullCount = 0;
        List<String> suspiciousValues = new ArrayList<>();

        for (Map.Entry<Integer, Object> entry : data.entrySet()) {
            Object value = entry.getValue();
            if (value == null) {
                nullCount++;
                continue;
            }

            String str = value.toString().trim();
            if (str.isEmpty()) {
                nullCount++;
                continue;
            }

            // 检查是否是数字（可能是数据行）
            if (str.matches("\\d+\\.\\d+") || str.matches("\\d+")) {
                numberCount++;
                suspiciousValues.add(str);
            }
            // 检查是否是日期（可能是数据行）
            else if (str.matches("\\d{4}[/-]\\d{1,2}[/-]\\d{1,2}")) {
                numberCount++;
                suspiciousValues.add(str);
            }
            // 检查是否是中文或英文文本（可能是表头）
            else if (str.matches(".*[\\u4e00-\\u9fa5a-zA-Z].*")) {
                textCount++;
            } else {
                numberCount++;
                suspiciousValues.add(str);
            }
        }

        int totalCount = textCount + numberCount + nullCount;
        boolean looksLikeHeader = textCount > numberCount && textCount > totalCount * 0.3;

        log.debug("表头数据验证: 文本数={}, 数字数={}, 空值数={}, 总数={}, 像表头={}",
                 textCount, numberCount, nullCount, totalCount, looksLikeHeader);

        if (!suspiciousValues.isEmpty()) {
            log.debug("可疑的数据值: {}", suspiciousValues);
        }

        return looksLikeHeader;
    }

    /**
     * 读取表头并建立列名到列索引的映射 - 增强版本
     */
    private void readHeader(Map<Integer, Object> headerData) {
        log.info("=== 开始读取Excel表头 ===");
        log.info("表头原始数据: {}", headerData);

        // 验证表头数据
        boolean looksLikeHeader = validateHeaderLikeData(headerData);
        if (!looksLikeHeader) {
            log.warn("⚠️ 警告：当前行数据不像表头，可能是数据行！");
            log.warn("请检查th_line配置是否正确，当前配置: {}", template.getThLine());
        } else {
            log.info("✅ 表头数据验证通过，看起来像有效的表头");
        }

        headerMapping.clear();

        // 建立列名到列索引的映射
        for (Map.Entry<Integer, Object> entry : headerData.entrySet()) {
            Integer columnIndex = entry.getKey();
            Object columnNameObj = entry.getValue();

            if (columnNameObj != null) {
                String columnName = columnNameObj.toString().trim();
                if (!columnName.isEmpty()) {
                    headerMapping.put(columnName, columnIndex);
                    log.info("表头映射: '{}' -> 列索引 {}", columnName, columnIndex);
                } else {
                    log.warn("列索引 {} 的列名为空字符串", columnIndex);
                }
            } else {
                log.warn("列索引 {} 的列名为null", columnIndex);
            }
        }

        headerRead = true;
        log.info("表头读取完成，共映射 {} 个列名", headerMapping.size());
        log.info("实际读取到的列名: {}", headerMapping.keySet());

        // 检查是否包含坐标相关的列名
        checkCoordinateColumns();

        // 验证模板配置中的列名是否都存在
        try {
            validateColumnNames();
            log.info("列名验证通过");
        } catch (Exception e) {
            log.error("列名验证失败: {}", e.getMessage());
            // 在验证模式下，列名验证失败应该抛出异常
            if ("valid".equals(target)) {
                throw e;
            }
        }
        log.info("=== 表头读取完成 ===");

        // 表头读取完成后，立即预处理映射配置
        preprocessMappingConfigurations();
    }

    /**
     * 预处理映射配置，提高后续数据处理性能
     */
    private void preprocessMappingConfigurations() {
        long startTime = System.currentTimeMillis();
        log.info("=== 开始预处理映射配置 ===");

        // 1. 预处理字段映射
        preprocessFieldMappings();

        // 2. 预处理坐标映射
        preprocessCoordinateMappings();

        long endTime = System.currentTimeMillis();
        log.info("=== 映射配置预处理完成，耗时: {}ms ===", endTime - startTime);
    }

    /**
     * 预处理字段映射配置
     */
    private void preprocessFieldMappings() {
        fieldMappingCache.clear();

        List<Map<String, Object>> fieldMappings = template.getMap();
        if (fieldMappings == null) {
            log.warn("字段映射配置为空");
            return;
        }

        int validMappings = 0;
        int skippedMappings = 0;

        for (Map<String, Object> mapping : fieldMappings) {
            String columnName = (String) mapping.get("columnName");
            String fieldName = (String) mapping.get("fieldName");
            String fieldType = (String) mapping.get("fieldType");
            Boolean checked = (Boolean) mapping.get("checked");

            // 跳过未启用的字段映射
            if (checked != null && !checked) {
                skippedMappings++;
                continue;
            }

            // 跳过无效的字段名
            if (fieldName == null || fieldName.trim().isEmpty()) {
                skippedMappings++;
                continue;
            }

            FieldMappingCache cache = new FieldMappingCache(fieldName, columnName, fieldType, checked);

            // 预解析列索引（只支持列名映射）
            if (columnName != null && !columnName.trim().isEmpty()) {
                cache.columnIndex = headerMapping.get(columnName.trim());
                cache.mappingSource = "列名'" + columnName + "'";

                if (cache.columnIndex == null) {
                    log.warn("预处理时未找到列名'{}' (字段: {})，可用列名: {}",
                            columnName, fieldName, headerMapping.keySet());
                    skippedMappings++;
                    continue;
                }
            } else {
                log.warn("Excel字段映射无效：fieldName={}, columnName为空", fieldName);
                skippedMappings++;
                continue;
            }

            fieldMappingCache.put(fieldName, cache);
            validMappings++;

            log.debug("预处理字段映射: fieldName={}, columnIndex={}, source={}",
                     fieldName, cache.columnIndex, cache.mappingSource);
        }

        log.info("字段映射预处理完成: 有效映射={}, 跳过映射={}", validMappings, skippedMappings);
    }

    /**
     * 预处理坐标映射配置
     */
    private void preprocessCoordinateMappings() {
        coordinateMappingCache = new CoordinateMappingCache();

        // 预处理线坐标映射
        if (template.getType() != null && template.getType() == 3) {
            Map<String, Object> lineMap = template.getLineMap();
            if (lineMap != null) {
                coordinateMappingCache.xColumnIndex = getCoordinateColumnIndexFromMap(lineMap, "x");
                coordinateMappingCache.yColumnIndex = getCoordinateColumnIndexFromMap(lineMap, "y");
                coordinateMappingCache.x1ColumnIndex = getCoordinateColumnIndexFromMap(lineMap, "x1");
                coordinateMappingCache.y1ColumnIndex = getCoordinateColumnIndexFromMap(lineMap, "y1");

                coordinateMappingCache.hasValidLineMapping =
                    coordinateMappingCache.xColumnIndex != null &&
                    coordinateMappingCache.yColumnIndex != null &&
                    coordinateMappingCache.x1ColumnIndex != null &&
                    coordinateMappingCache.y1ColumnIndex != null;

                log.info("线坐标映射预处理: x={}, y={}, x1={}, y1={}, 有效={}",
                        coordinateMappingCache.xColumnIndex, coordinateMappingCache.yColumnIndex,
                        coordinateMappingCache.x1ColumnIndex, coordinateMappingCache.y1ColumnIndex,
                        coordinateMappingCache.hasValidLineMapping);
            }
        }

        // 预处理点坐标映射
        if (template.getType() != null && template.getType() == 2) {
            Map<String, Object> pointMap = template.getPointMap();
            if (pointMap != null) {
                coordinateMappingCache.pointXColumnIndex = getCoordinateColumnIndexFromMap(pointMap, "x");
                coordinateMappingCache.pointYColumnIndex = getCoordinateColumnIndexFromMap(pointMap, "y");

                coordinateMappingCache.hasValidPointMapping =
                    coordinateMappingCache.pointXColumnIndex != null &&
                    coordinateMappingCache.pointYColumnIndex != null;

                log.info("点坐标映射预处理: x={}, y={}, 有效={}",
                        coordinateMappingCache.pointXColumnIndex, coordinateMappingCache.pointYColumnIndex,
                        coordinateMappingCache.hasValidPointMapping);
            }
        }
    }

    /**
     * 从坐标映射中获取列索引（用于预处理）
     */
    private Integer getCoordinateColumnIndexFromMap(Map<String, Object> coordinateMap, String coordinateKey) {
        if (coordinateMap == null || coordinateKey == null) {
            return null;
        }

        Object coordinateValue = coordinateMap.get(coordinateKey);
        if (!(coordinateValue instanceof String)) {
            return null;
        }

        String columnName = ((String) coordinateValue).trim();
        if (columnName.isEmpty()) {
            return null;
        }

        return headerMapping.get(columnName);
    }

    /**
     * 检查表头中是否包含坐标相关的列名
     */
    private void checkCoordinateColumns() {
        // 检查线表坐标列名
        if (template.getType() != null && template.getType() == 3) {
            Map<String, Object> lineMap = template.getLineMap();
            if (lineMap != null) {
                String[] coordinateKeys = {"x", "y", "x1", "y1"};
                List<String> foundColumns = new ArrayList<>();
                List<String> missingColumns = new ArrayList<>();

                for (String key : coordinateKeys) {
                    Object columnNameObj = lineMap.get(key);
                    if (columnNameObj instanceof String) {
                        String columnName = ((String) columnNameObj).trim();
                        if (!columnName.isEmpty()) {
                            if (headerMapping.containsKey(columnName)) {
                                foundColumns.add(columnName);
                            } else {
                                missingColumns.add(columnName);
                            }
                        }
                    }
                }

                if (!foundColumns.isEmpty()) {
                    log.info("找到的线坐标列名: {}", foundColumns);
                }
                if (!missingColumns.isEmpty()) {
                    log.warn("缺失的线坐标列名: {}", missingColumns);
                }
            }
        }

        // 检查点表坐标列名
        if (template.getType() != null && template.getType() == 2) {
            Map<String, Object> pointMap = template.getPointMap();
            if (pointMap != null) {
                String[] coordinateKeys = {"x", "y"};
                List<String> foundColumns = new ArrayList<>();
                List<String> missingColumns = new ArrayList<>();

                for (String key : coordinateKeys) {
                    Object columnNameObj = pointMap.get(key);
                    if (columnNameObj instanceof String) {
                        String columnName = ((String) columnNameObj).trim();
                        if (!columnName.isEmpty()) {
                            if (headerMapping.containsKey(columnName)) {
                                foundColumns.add(columnName);
                            } else {
                                missingColumns.add(columnName);
                            }
                        }
                    }
                }

                if (!foundColumns.isEmpty()) {
                    log.info("找到的点坐标列名: {}", foundColumns);
                }
                if (!missingColumns.isEmpty()) {
                    log.warn("缺失的点坐标列名: {}", missingColumns);
                }
            }
        }
    }

    /**
     * 验证模板配置中的列名是否在Excel表头中存在
     */
    private void validateColumnNames() {
        List<String> missingColumns = new ArrayList<>();

        // 验证字段映射中的列名
        validateFieldMappingColumnNames(missingColumns);

        // 验证坐标映射中的列名
        validateCoordinateMappingColumnNames(missingColumns);

        if (!missingColumns.isEmpty()) {
            String errorMessage = String.format("Excel表中未找到以下列名: %s，请检查表头是否正确",
                String.join(", ", missingColumns.stream().map(name -> "'" + name + "'").toArray(String[]::new)));
            throw new RuntimeException(errorMessage);
        }

        log.info("列名验证通过，所有配置的列名都在Excel表头中找到");
    }

    /**
     * 验证字段映射中的列名
     */
    private void validateFieldMappingColumnNames(List<String> missingColumns) {
        List<Map<String, Object>> fieldMappings = template.getMap();
        if (fieldMappings == null) {
            return;
        }

        for (Map<String, Object> mapping : fieldMappings) {
            Boolean checked = (Boolean) mapping.get("checked");
            String columnName = (String) mapping.get("columnName");
            String fieldName = (String) mapping.get("fieldName");

            // 只验证启用的字段映射
            if ((checked == null || checked) && columnName != null && !columnName.trim().isEmpty()) {
                if (!headerMapping.containsKey(columnName)) {
                    missingColumns.add(columnName);
                    log.error("Excel表中未找到列名'{}' (映射到字段: {})", columnName, fieldName);
                }
            }
        }
    }

    /**
     * 验证坐标映射中的列名
     * 坐标字段通过point_map和line_map配置进行处理
     */
    private void validateCoordinateMappingColumnNames(List<String> missingColumns) {
        // 验证点坐标映射
        if (template.getType() != null && template.getType() == 2) {
            validateCoordinateMapColumnNames(template.getPointMap(), missingColumns, "点坐标",
                new String[]{"x", "y"});
        }

        // 验证线坐标映射
        if (template.getType() != null && template.getType() == 3) {
            validateCoordinateMapColumnNames(template.getLineMap(), missingColumns, "线坐标",
                new String[]{"x", "y", "x1", "y1"});
        }
    }



    /**
     * 验证坐标映射配置中的列名（强制列名格式）
     */
    private void validateCoordinateMapColumnNames(Map<String, Object> coordinateMap,
                                                List<String> missingColumns,
                                                String mapType,
                                                String[] coordinateKeys) {
        if (coordinateMap == null) {
            throw new RuntimeException(String.format("%s坐标映射配置不能为空", mapType));
        }

        for (String key : coordinateKeys) {
            Object value = coordinateMap.get(key);

            // 强制验证配置值必须是字符串
            if (value == null) {
                throw new RuntimeException(String.format(
                    "%s坐标字段 '%s' 的配置值不能为空。请使用列名字符串配置，例如: \"%s\": \"起点横坐标\"",
                    mapType, key, key));
            }

            if (!(value instanceof String)) {
                throw new RuntimeException(String.format(
                    "%s坐标字段 '%s' 的配置值必须是字符串类型的列名，当前类型: %s，当前值: %s。" +
                    "正确的配置格式示例: \"%s\": \"起点横坐标\"",
                    mapType, key, value.getClass().getSimpleName(), value, key));
            }

            String columnName = ((String) value).trim();
            if (columnName.isEmpty()) {
                throw new RuntimeException(String.format(
                    "%s坐标字段 '%s' 的列名不能为空字符串。请配置有效的Excel列名，例如: \"%s\": \"起点横坐标\"",
                    mapType, key, key));
            }

            // 检查列名是否在表头中存在
            if (!headerMapping.containsKey(columnName)) {
                missingColumns.add(columnName);
                log.error("Excel表中未找到{}列名'{}' (坐标字段: {})", mapType, columnName, key);
            }
        }
    }

    /**
     * 性能统计信息
     */
    private static class PerformanceStats {
        long totalProcessingTime = 0;
        long headerProcessingTime = 0;
        long mappingPreprocessingTime = 0;
        long dataConversionTime = 0;
        long coordinateProcessingTime = 0;
        int processedRows = 0;
        int skippedRows = 0;
        long maxRowProcessingTime = 0;
        int slowRowIndex = -1;

        void recordRowProcessing(int rowIndex, long processingTime) {
            totalProcessingTime += processingTime;
            processedRows++;

            if (processingTime > maxRowProcessingTime) {
                maxRowProcessingTime = processingTime;
                slowRowIndex = rowIndex;
            }
        }

        void logStats() {
            log.info("=== Excel数据处理性能统计 ===");
            log.info("总处理时间: {}ms", totalProcessingTime);
            log.info("表头处理时间: {}ms", headerProcessingTime);
            log.info("映射预处理时间: {}ms", mappingPreprocessingTime);
            log.info("数据转换时间: {}ms", dataConversionTime);
            log.info("坐标处理时间: {}ms", coordinateProcessingTime);
            log.info("处理行数: {}", processedRows);
            log.info("跳过行数: {}", skippedRows);
            if (processedRows > 0) {
                log.info("平均每行处理时间: {:.2f}ms", (double) totalProcessingTime / processedRows);
                log.info("最慢行处理时间: {}ms (第{}行)", maxRowProcessingTime, slowRowIndex);
            }
            log.info("=== 性能统计结束 ===");
        }
    }

    private final PerformanceStats performanceStats = new PerformanceStats();



    /**
     * 坐标转换
     */
    private void transformCoordinates(List<GeoFeatureEntity> entities) {
        String sourceCoordSystem = template.getOriginalCoordinateSystem();
        String targetCoordSystem = template.getTargetCoordinateSystem();

        if (sourceCoordSystem == null || targetCoordSystem == null || sourceCoordSystem.equals(targetCoordSystem)) {
            log.debug("跳过坐标转换: 源坐标系={}, 目标坐标系={}", sourceCoordSystem, targetCoordSystem);
            return;
        }

        log.info("开始批量坐标转换: {} -> {}, 实体数量: {}", sourceCoordSystem, targetCoordSystem, entities.size());

        for (GeoFeatureEntity entity : entities) {
            try {
                // 检查是否为Excel模板
                if ("excel".equalsIgnoreCase(template.getTemplateType())) {
                    transformExcelCoordinates(entity, sourceCoordSystem, targetCoordSystem);
                } else {
                    // 传统的几何转换方式（用于Shapefile等）
                    transformGeometry(entity, sourceCoordSystem, targetCoordSystem);
                }
            } catch (Exception e) {
                log.error("坐标转换失败，要素ID: {}, 错误: {}", entity.getFeatureId(), e.getMessage(), e);
            }
        }

        log.info("批量坐标转换完成");
    }

    /**
     * Excel模板的坐标转换
     */
    private void transformExcelCoordinates(GeoFeatureEntity entity, String sourceCoordSystem, String targetCoordSystem) {
        if (template.getType() == null) {
            return;
        }

        switch (template.getType()) {
            case 2: // 点表
                transformExcelPointCoordinates(entity, sourceCoordSystem, targetCoordSystem);
                break;
            case 3: // 线表
                transformExcelLineCoordinates(entity, sourceCoordSystem, targetCoordSystem);
                break;
            default:
                // type=1 纯文本，不需要坐标转换
                log.debug("纯文本模板，跳过坐标转换");
                break;
        }
    }

    /**
     * Excel点表坐标转换
     */
    private void transformExcelPointCoordinates(GeoFeatureEntity entity, String sourceCoordSystem, String targetCoordSystem) {
        Object xCoord = entity.getAttribute("x_coord");
        Object yCoord = entity.getAttribute("y_coord");

        if (xCoord == null || yCoord == null) {
            log.debug("点坐标为空，跳过转换: x={}, y={}", xCoord, yCoord);
            return;
        }

        try {
            double x = Double.parseDouble(xCoord.toString());
            double y = Double.parseDouble(yCoord.toString());

            // 构建POINT几何进行转换
            String originalWkt = String.format("POINT(%f %f)", x, y);
            String transformedWkt = coordinateService.transformGeometry(originalWkt, sourceCoordSystem, targetCoordSystem);

            // 解析转换后的坐标
            double[] transformedCoords = parsePointFromWkt(transformedWkt);
            if (transformedCoords != null) {
                double newX = transformedCoords[0];
                double newY = transformedCoords[1];

                // 更新坐标属性
                entity.setAttribute("x_coord", newX);
                entity.setAttribute("y_coord", newY);

                // 更新原始字段映射，确保数据库插入时使用转换后的坐标
                updateTransformedPointCoordinates(entity, newX, newY);

                // 更新几何
                entity.setGeometry(transformedWkt);

                log.debug("点坐标转换完成: ({}, {}) -> ({}, {})", x, y, newX, newY);
            }
        } catch (Exception e) {
            log.warn("点坐标转换失败: x={}, y={}, 错误: {}", xCoord, yCoord, e.getMessage());
        }
    }

    /**
     * 更新转换后的点坐标到原始字段映射 - 性能优化版本
     */
    private void updateTransformedPointCoordinates(GeoFeatureEntity entity, double x, double y) {
        // 使用预处理的坐标映射缓存（性能优化）
        if (coordinateMappingCache == null || !coordinateMappingCache.hasValidPointMapping) {
            return;
        }

        // 直接使用预解析的列索引（避免重复解析）
        Integer xColumn = coordinateMappingCache.pointXColumnIndex;
        Integer yColumn = coordinateMappingCache.pointYColumnIndex;

        // 更新原始字段，确保数据库插入时使用转换后的坐标
        if (xColumn != null) {
            String xFieldKey = "col_" + xColumn;
            entity.setAttribute(xFieldKey, x);
            log.debug("更新转换后X字段 {}: {}", xFieldKey, x);
        }

        if (yColumn != null) {
            String yFieldKey = "col_" + yColumn;
            entity.setAttribute(yFieldKey, y);
            log.debug("更新转换后Y字段 {}: {}", yFieldKey, y);
        }
    }

    /**
     * Excel线表坐标转换
     */
    private void transformExcelLineCoordinates(GeoFeatureEntity entity, String sourceCoordSystem, String targetCoordSystem) {
        Object xCoord = entity.getAttribute("x_coord");
        Object yCoord = entity.getAttribute("y_coord");
        Object x1Coord = entity.getAttribute("x1_coord");
        Object y1Coord = entity.getAttribute("y1_coord");

        if (xCoord == null || yCoord == null || x1Coord == null || y1Coord == null) {
            log.debug("线坐标不完整，跳过转换: x={}, y={}, x1={}, y1={}", xCoord, yCoord, x1Coord, y1Coord);
            return;
        }

        try {
            double x = Double.parseDouble(xCoord.toString());
            double y = Double.parseDouble(yCoord.toString());
            double x1 = Double.parseDouble(x1Coord.toString());
            double y1 = Double.parseDouble(y1Coord.toString());

            // 构建LINESTRING几何进行转换
            String originalWkt = String.format("LINESTRING(%f %f, %f %f)", x, y, x1, y1);
            String transformedWkt = coordinateService.transformGeometry(originalWkt, sourceCoordSystem, targetCoordSystem);

            // 解析转换后的坐标
            double[][] transformedCoords = parseLineFromWkt(transformedWkt);
            if (transformedCoords != null && transformedCoords.length >= 2) {
                double newX = transformedCoords[0][0];
                double newY = transformedCoords[0][1];
                double newX1 = transformedCoords[1][0];
                double newY1 = transformedCoords[1][1];

                // 更新坐标属性
                entity.setAttribute("x_coord", newX);
                entity.setAttribute("y_coord", newY);
                entity.setAttribute("x1_coord", newX1);
                entity.setAttribute("y1_coord", newY1);

                // 更新原始字段映射，确保数据库插入时使用转换后的坐标
                updateTransformedLineCoordinates(entity, newX, newY, newX1, newY1);

                // 更新几何
                entity.setGeometry(transformedWkt);

                log.debug("线坐标转换完成: ({}, {}) - ({}, {}) -> ({}, {}) - ({}, {})",
                         x, y, x1, y1, newX, newY, newX1, newY1);
            }
        } catch (Exception e) {
            log.warn("线坐标转换失败: x={}, y={}, x1={}, y1={}, 错误: {}", xCoord, yCoord, x1Coord, y1Coord, e.getMessage());
        }
    }

    /**
     * 更新转换后的线坐标到原始字段映射 - 性能优化版本
     */
    private void updateTransformedLineCoordinates(GeoFeatureEntity entity, double x, double y, double x1, double y1) {
        // 使用预处理的坐标映射缓存（性能优化）
        if (coordinateMappingCache == null || !coordinateMappingCache.hasValidLineMapping) {
            return;
        }

        // 直接使用预解析的列索引（避免重复解析）
        Integer xColumn = coordinateMappingCache.xColumnIndex;
        Integer yColumn = coordinateMappingCache.yColumnIndex;
        Integer x1Column = coordinateMappingCache.x1ColumnIndex;
        Integer y1Column = coordinateMappingCache.y1ColumnIndex;

        // 更新原始字段，确保数据库插入时使用转换后的坐标
        if (xColumn != null) {
            String xFieldKey = "col_" + xColumn;
            entity.setAttribute(xFieldKey, x);
            log.debug("更新转换后起点X字段 {}: {}", xFieldKey, x);
        }

        if (yColumn != null) {
            String yFieldKey = "col_" + yColumn;
            entity.setAttribute(yFieldKey, y);
            log.debug("更新转换后起点Y字段 {}: {}", yFieldKey, y);
        }

        if (x1Column != null) {
            String x1FieldKey = "col_" + x1Column;
            entity.setAttribute(x1FieldKey, x1);
            log.debug("更新转换后终点X字段 {}: {}", x1FieldKey, x1);
        }

        if (y1Column != null) {
            String y1FieldKey = "col_" + y1Column;
            entity.setAttribute(y1FieldKey, y1);
            log.debug("更新转换后终点Y字段 {}: {}", y1FieldKey, y1);
        }
    }

    /**
     * 传统几何转换（用于Shapefile等）
     */
    private void transformGeometry(GeoFeatureEntity entity, String sourceCoordSystem, String targetCoordSystem) {
        String geometry = entity.getGeometry();
        if (geometry != null && !geometry.trim().isEmpty()) {
            String transformedGeometry = coordinateService.transformGeometry(geometry, sourceCoordSystem, targetCoordSystem);
            entity.setGeometry(transformedGeometry);
        }
    }

    /**
     * 插入批次数据
     */
    private void insertBatch(List<GeoFeatureEntity> entities) {
        log.info("开始插入批次数据，实体数量: {}", entities.size());

        if (batchInsertService == null) {
            log.error("批量插入服务为null，无法插入数据");
            throw new RuntimeException("批量插入服务未初始化");
        }

        // 打印第一个实体的详细信息用于调试
        if (!entities.isEmpty()) {
            GeoFeatureEntity firstEntity = entities.get(0);
            log.info("第一个实体详情: featureId={}, attributes={}, geometry={}",
                    firstEntity.getFeatureId(), firstEntity.getRawAttributes(), firstEntity.getGeometry());
        }

        try {
            // 不在这里设置数据源，由外层调用方负责设置
            log.info("调用批量插入服务: zeroConversionBatchInsert，目标数据库: {}", template.getDataBase());

            // 传递表头映射以确保字段映射正确
            batchInsertService.zeroConversionBatchInsert(entities, template, headerMapping);
            log.info("批量插入服务调用完成");
        } catch (Exception e) {
            log.error("批量插入失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理导出批次
     */
    private void processExportBatch(List<GeoFeatureEntity> entities) {
        // 导出模式的特殊处理逻辑
        log.debug("处理导出批次，数据量: {}", entities.size());
    }

    /**
     * 值类型转换
     */
    private Object convertValue(Object value, String fieldType) {
        if (value == null) {
            return null;
        }

        try {
            switch (fieldType.toLowerCase()) {
                case "string":
                case "varchar":
                case "text":
                    return value.toString();
                case "integer":
                case "int":
                    return Integer.valueOf(value.toString());
                case "double":
                case "float":
                case "decimal":
                    return Double.valueOf(value.toString());
                case "boolean":
                    return Boolean.valueOf(value.toString());
                default:
                    return value;
            }
        } catch (Exception e) {
            log.warn("值转换失败，原始值: {}, 目标类型: {}", value, fieldType);
            return value;
        }
    }

    /**
     * 设置验证模式标识
     * @param validationOnly true表示只验证不插入数据库
     */
    public void setValidationOnly(boolean validationOnly) {
        this.validationOnly = validationOnly;
        log.info("设置Excel验证模式: validationOnly={}", validationOnly);
    }

    /**
     * 设置跳过验证标识
     * @param skipValidation true表示跳过验证直接插入
     */
    public void setSkipValidation(boolean skipValidation) {
        this.skipValidation = skipValidation;
        log.info("设置Excel跳过验证模式: skipValidation={}", skipValidation);
    }



    /**
     * 详细验证错误记录
     */
    @Data
    @AllArgsConstructor
    public static class DetailedValidationError {
        private int rowNumber;              // 行号（从1开始）
        private int rowIndex;               // 行索引（从0开始）
        private int columnIndex;            // 列索引（从0开始）
        private String errorMessage;        // 错误信息
        private String errorType;          // 错误类型
        private Map<Integer, Object> originalData; // 原始行数据
        private String fieldName;          // 出错字段名
        private Object fieldValue;         // 出错字段值

        // 兼容性构造函数
        public DetailedValidationError(int rowNumber, String errorMessage, String errorType,
                                     Map<Integer, Object> originalData, String fieldName, Object fieldValue) {
            this.rowNumber = rowNumber;
            this.rowIndex = rowNumber - 1; // 行索引从0开始
            this.columnIndex = 0; // 默认列索引
            this.errorMessage = errorMessage;
            this.errorType = errorType;
            this.originalData = originalData;
            this.fieldName = fieldName;
            this.fieldValue = fieldValue;
        }
    }

}
