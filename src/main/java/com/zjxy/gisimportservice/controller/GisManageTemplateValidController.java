package com.zjxy.gisimportservice.controller;

import com.zjxy.framework.response.Result;
import com.zjxy.gisimportservice.entity.GisManageTemplateValid;
import com.zjxy.gisimportservice.service.GisManageTemplateValidService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字段验证控制器
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-29
 */
@Slf4j
@RestController
@RequestMapping("/api/valid")
@Api(tags = "字段校验")
public class GisManageTemplateValidController {

    @Resource
    private GisManageTemplateValidService validService;

    @PostMapping("/addValid")
    @ApiOperation("添加字段校验")
    public Result<Integer> addValid(@RequestBody GisManageTemplateValid valid) {
        try {
            log.info("添加字段校验 - 模板ID: {}, UID: {}", valid.getTemplateId(), valid.getUid());
            Integer id = validService.addValid(valid);
            return Result.SUCCESS(id);
        } catch (Exception e) {
            log.error("添加字段校验失败", e);
            return Result.FAILURE("添加字段校验失败: " + e.getMessage());
        }
    }

    @PostMapping("/addValidBatch")
    @ApiOperation("批量添加字段校验")
    public Result<Boolean> addValidBatch(@RequestBody List<GisManageTemplateValid> validList) {
        try {
            log.info("批量添加字段校验 - 数量: {}", validList.size());
            validService.addValidBatch(validList);
            return Result.SUCCESS(true);
        } catch (Exception e) {
            log.error("批量添加字段校验失败", e);
            return Result.FAILURE("批量添加字段校验失败: " + e.getMessage());
        }
    }

    @PutMapping("/updateValid")
    @ApiOperation("更新字段校验")
    public Result<Boolean> updateValid(@RequestBody GisManageTemplateValid valid) {
        try {
            log.info("更新字段校验 - ID: {}", valid.getId());
            Boolean success = validService.updateValid(valid);
            return Result.SUCCESS(success);
        } catch (Exception e) {
            log.error("更新字段校验失败", e);
            return Result.FAILURE("更新字段校验失败: " + e.getMessage());
        }
    }

    @PutMapping("/updateValidBatch")
    @ApiOperation("批量更新字段校验")
    public Result<Boolean> updateValidBatch(@RequestBody List<GisManageTemplateValid> validList) {
        try {
            log.info("批量更新字段校验 - 数量: {}", validList.size());
            Boolean success = validService.updateValidBatch(validList);
            return Result.SUCCESS(success);
        } catch (Exception e) {
            log.error("批量更新字段校验失败", e);
            return Result.FAILURE("批量更新字段校验失败: " + e.getMessage());
        }
    }

    @GetMapping("/getValid")
    @ApiOperation("获取字段校验")
    public Result<List<GisManageTemplateValid>> getValid(
            @ApiParam("模板唯一标识") @RequestParam(required = false) String uid,
            @ApiParam("模板ID") @RequestParam(required = false) String templateId) {
        try {
            log.info("查询字段校验 - UID: {}, 模板ID: {}", uid, templateId);
            List<GisManageTemplateValid> result = validService.getValid(uid, templateId);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("查询字段校验失败", e);
            return Result.FAILURE("查询字段校验失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delValid")
    @ApiOperation("删除字段校验")
    public Result<Boolean> delValid(@ApiParam("验证规则ID") @RequestParam Integer id) {
        try {
            log.info("删除字段校验 - ID: {}", id);
            Boolean success = validService.delValid(id);
            return Result.SUCCESS(success);
        } catch (Exception e) {
            log.error("删除字段校验失败", e);
            return Result.FAILURE("删除字段校验失败: " + e.getMessage());
        }
    }

    @GetMapping("/getValidByUid")
    @ApiOperation("通过uid获取对应验证模版")
    public Result<List<GisManageTemplateValid>> getValidByUid(
            @ApiParam("模板唯一标识") @RequestParam String uid) {
        try {
            log.info("通过UID查询字段校验 - UID: {}", uid);
            List<GisManageTemplateValid> result = validService.getValidByUid(uid);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("通过UID查询字段校验失败", e);
            return Result.FAILURE("通过UID查询字段校验失败: " + e.getMessage());
        }
    }

    @GetMapping("/getValidByTemplateId")
    @ApiOperation("根据模板ID获取验证配置")
    public Result<GisManageTemplateValid> getValidByTemplateId(
            @ApiParam("模板ID") @RequestParam Integer templateId) {
        try {
            log.info("根据模板ID查询验证配置 - 模板ID: {}", templateId);
            GisManageTemplateValid result = validService.getValidByTemplateId(templateId);
            return Result.SUCCESS(result);
        } catch (Exception e) {
            log.error("根据模板ID查询验证配置失败", e);
            return Result.FAILURE("根据模板ID查询验证配置失败: " + e.getMessage());
        }
    }

}
