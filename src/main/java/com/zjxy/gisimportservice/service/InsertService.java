package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;

import java.util.List;
import java.util.Map;

/**
 * 数据插入服务接口
 * 
 * 提供高性能的批量数据插入功能，包括：
 * - 零转换超高速批量插入
 * - 动态表头映射支持
 * - 自动类型转换和优化
 * - 批次处理和性能监控
 * 
 * <AUTHOR> Data Import System
 * @since 2024-08-04
 */
public interface InsertService {

    /**
     * 零转换超高速批量插入（基于动态表头映射）
     * 
     * 该方法提供高性能的批量数据插入功能，通过零转换模式直接使用原始值，
     * 跳过不必要的类型转换以提升插入速度。支持Excel和SHP文件的数据插入。
     * 
     * @param entities 实体列表，包含要插入的地理要素数据
     * @param template 模板配置，定义目标表结构和字段映射规则
     * @param headerMapping Excel表头映射（列名->列索引），Excel模板必需此参数，SHP模板可为null
     * @return 插入结果映射，包含以下字段：
     *         - success: 是否成功 (Boolean)
     *         - insertedCount: 成功插入的记录数 (Integer)
     *         - totalTime: 总耗时毫秒数 (Long)
     *         - speed: 插入速度（条/秒） (Double)
     *         - conversionMode: 转换模式描述 (String)
     *         - message: 错误信息（失败时） (String)
     *         - errorDetails: 错误详情（失败时） (String)
     * 
     * @throws IllegalArgumentException 当Excel模板未提供headerMapping参数时
     * @throws RuntimeException 当数据库插入操作失败时
     */
    Map<String, Object> zeroConversionBatchInsert(List<GeoFeatureEntity> entities,
                                                 GisManageTemplate template,
                                                 Map<String, Integer> headerMapping);
}
