package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.GisManageTemplateValid;
import com.zjxy.gisimportservice.entity.ValidationResult;

/**
 * 数据验证服务接口
 * 提供完整的数据检查和验证功能
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-18
 */
public interface DataValidationService {
    /**
     * 验证Shapefile数据
     *
     * @param filePath 文件路径
     * @param template 模板配置
     * @param task 导入任务
     * @return 验证结果
     */
    ValidationResult validateShapefileData(String filePath, GisManageTemplate template, GisImportTask task);
    /**
     * 统一的模板验证方法（与文件格式无关）
     * 基于GisManageTemplateValid的sx属性进行验证
     *
     * @param data 数据对象（可以是GeoFeatureEntity或其他数据结构）
     * @param template 模板配置
     * @param rowIndex 行号
     * @param detailedErrors 详细错误收集器
     * @return 验证是否通过
     */
    boolean validateDataWithTemplate(Object data, GisManageTemplate template, int rowIndex,
                                   java.util.List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors);
    /**
     * 获取模板的验证配置
     *
     * @param templateId 模板ID
     * @return 验证配置
     */
    GisManageTemplateValid getValidationConfig(Integer templateId);

    /**
     * 统一的字段验证方法
     *
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param fieldConfig 字段配置
     * @param rowIndex 行号
     * @param detailedErrors 详细错误收集器
     * @return 验证是否通过
     */
    boolean validateField(String fieldName, Object fieldValue, java.util.Map<String, Object> fieldConfig,
                         int rowIndex, java.util.List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors);

    /**
     * 收集详细错误信息
     *
     * @param rowNumber 行号
     * @param errorMessage 错误信息
     * @param errorType 错误类型
     * @param originalData 原始数据
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param detailedErrors 详细错误收集器
     */
    void collectDetailedError(int rowNumber, String errorMessage, String errorType,
                            java.util.Map<Integer, Object> originalData, String fieldName, Object fieldValue,
                            java.util.List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors);

}
