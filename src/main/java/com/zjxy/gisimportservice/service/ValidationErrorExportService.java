package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.listener.ExcelDataListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 验证错误Excel导出服务
 * 基于原始Excel文件的错误标注方式
 */
@Slf4j
@Service
public class ValidationErrorExportService {

    @Autowired
    private ExcelAnnotationService excelAnnotationService;

    /**
     * 导出验证错误到Excel文件（新版本 - 基于原文件标注）
     *
     * @param detailedErrors 详细错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param originalFilePath 原始Excel文件路径
     * @param headerRowIndex 表头行索引（从0开始）
     * @param template 模板配置（用于字段映射）
     * @return 生成的标注文件路径
     */
    public String exportValidationErrors(List<ExcelDataListener.DetailedValidationError> detailedErrors,
                                       Long taskId, String taskName, String originalFilePath,
                                       int headerRowIndex, GisManageTemplate template) {
        try {
            log.info("开始生成基于原文件的错误标注Excel - 原文件: {}, 错误数: {}",
                     originalFilePath, detailedErrors.size());

            // 使用ExcelAnnotationService生成标注文件
            String annotatedFilePath = excelAnnotationService.generateAnnotatedExcel(
                    originalFilePath, detailedErrors, taskId, taskName, headerRowIndex, template);

            log.info("基于原文件的错误标注Excel生成成功: {}", annotatedFilePath);
            return annotatedFilePath;

        } catch (Exception e) {
            log.error("生成基于原文件的错误标注Excel失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成错误标注文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为SHP文件生成Excel错误标注文件
     *
     * @param shpEntities SHP实体数据列表
     * @param detailedErrors 详细错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param template 模板配置
     * @return 生成的Excel标注文件路径
     */
    public String exportShpValidationErrors(List<GeoFeatureEntity> shpEntities,
                                          List<ExcelDataListener.DetailedValidationError> detailedErrors,
                                          Long taskId, String taskName, GisManageTemplate template) {
        try {
            log.info("开始生成SHP文件Excel错误标注文件 - 实体数: {}, 错误数: {}",
                     shpEntities.size(), detailedErrors.size());

            // 使用ExcelAnnotationService生成SHP错误标注文件
            String annotatedFilePath = excelAnnotationService.generateShpErrorExcel(
                    shpEntities, detailedErrors, taskId, taskName, template);

            log.info("SHP文件Excel错误标注文件生成成功: {}", annotatedFilePath);
            return annotatedFilePath;

        } catch (Exception e) {
            log.error("生成SHP文件Excel错误标注文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成SHP错误标注文件失败: " + e.getMessage(), e);
        }
    }

}
