package com.zjxy.gisimportservice.service.Impl;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.listener.ExcelDataListener;
import com.zjxy.gisimportservice.service.ExcelAnnotationService;
import com.zjxy.gisimportservice.util.CellAnnotationHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import java.io.IOException;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel错误标注服务实现类
 *
 * 基于原始Excel文件生成带有错误标注的新文件
 * 支持SHP和Excel数据的错误标注
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-30
 */
@Slf4j
@Service
public class ExcelAnnotationServiceImpl implements ExcelAnnotationService {

    private static final String ANNOTATED_FILE_DIR = "D:/xinyu_shixi/temp/validation-errors/";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    /**
     * 基于原始Excel文件生成错误标注文件
     *
     * @param originalFilePath 原始Excel文件路径
     * @param errors 验证错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param headerRowIndex 表头行索引（从0开始）
     * @param template 模板配置（用于字段映射）
     * @return 生成的标注文件路径
     */
    @Override
    public String generateAnnotatedExcel(String originalFilePath,
                                       List<ExcelDataListener.DetailedValidationError> errors,
                                       Long taskId, String taskName, int headerRowIndex, GisManageTemplate template) {
        try {
            log.info("开始生成Excel错误标注文件 - 原文件: {}, 错误数: {}", originalFilePath, errors.size());

            // 1. 确保输出目录存在
            Path dirPath = Paths.get(ANNOTATED_FILE_DIR);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }

            // 2. 生成输出文件路径
            String outputFilePath = generateOutputFilePath(originalFilePath, taskId, taskName);

            // 3. 读取原始Excel文件
            try (FileInputStream fis = new FileInputStream(originalFilePath);
                 XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

                // 4. 获取第一个工作表（假设数据在第一个sheet中）
                Sheet sheet = workbook.getSheetAt(0);

                // 5. 创建绘图对象用于添加注释
                Drawing<?> drawing = sheet.createDrawingPatriarch();

                // 6. 构建模板字段名到列索引的映射
                Map<String, Integer> fieldColumnMap = buildFieldColumnMap(sheet, headerRowIndex, template);

                // 7. 应用错误标注
                applyErrorAnnotations(sheet, workbook, drawing, errors, fieldColumnMap);

                // 8. 保存标注后的文件
                try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                    workbook.write(fos);
                }
            }

            log.info("Excel错误标注文件生成成功: {}", outputFilePath);
            return outputFilePath;

        } catch (Exception e) {
            log.error("生成Excel错误标注文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成错误标注文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成输出文件路径
     */
    private String generateOutputFilePath(String originalFilePath, Long taskId, String taskName) {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        String originalFileName = Paths.get(originalFilePath).getFileName().toString();
        String nameWithoutExt = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
        String extension = originalFileName.substring(originalFileName.lastIndexOf('.'));

        String fileName = String.format("%s_validated_%s%s", nameWithoutExt, timestamp, extension);
        return ANNOTATED_FILE_DIR + fileName;
    }

    /**
     * 构建模板字段名到列索引的映射
     *
     * @param sheet Excel工作表
     * @param headerRowIndex 表头行索引（从0开始）
     * @param template 模板配置（用于获取字段映射）
     * @return 模板字段名到列索引的映射
     */
    private Map<String, Integer> buildFieldColumnMap(Sheet sheet, int headerRowIndex, GisManageTemplate template) {
        Map<String, Integer> fieldColumnMap = new HashMap<>();

        // 1. 先构建Excel列名到列索引的映射
        Map<String, Integer> headerMapping = new HashMap<>();
        Row headerRow = sheet.getRow(headerRowIndex);
        if (headerRow != null) {
            for (Cell cell : headerRow) {
                if (cell != null && cell.getCellType() == CellType.STRING) {
                    String columnName = cell.getStringCellValue().trim();
                    headerMapping.put(columnName, cell.getColumnIndex());
                }
            }
        }

        log.debug("Excel表头映射: {}", headerMapping);

        // 2. 根据模板配置构建模板字段名到列索引的映射
        List<Map<String, Object>> fieldMappings = template.getMap();
        if (fieldMappings != null) {
            for (Map<String, Object> mapping : fieldMappings) {
                String fieldName = (String) mapping.get("fieldName");
                String columnName = (String) mapping.get("columnName");
                Boolean checked = (Boolean) mapping.get("checked");

                // 只处理启用的字段映射
                if (fieldName != null && columnName != null && (checked == null || checked)) {
                    Integer columnIndex = headerMapping.get(columnName.trim());
                    if (columnIndex != null) {
                        fieldColumnMap.put(fieldName, columnIndex);
                        log.debug("字段映射: {} -> 列名'{}' -> 列索引{}", fieldName, columnName, columnIndex);
                    } else {
                        log.warn("未找到列名'{}' (字段: {})，可用列名: {}", columnName, fieldName, headerMapping.keySet());
                    }
                }
            }
        }

        log.debug("构建模板字段列映射完成，表头行: {}, 映射数: {}", headerRowIndex + 1, fieldColumnMap.size());
        return fieldColumnMap;
    }

    /**
     * 应用错误标注到Excel文件
     *
     * @param sheet Excel工作表
     * @param workbook Excel工作簿
     * @param drawing 绘图对象
     * @param errors 验证错误列表
     * @param fieldColumnMap 字段名到列索引的映射
     */
    private void applyErrorAnnotations(Sheet sheet, Workbook workbook, Drawing<?> drawing,
                                     List<ExcelDataListener.DetailedValidationError> errors,
                                     Map<String, Integer> fieldColumnMap) {

        // 创建样式缓存，避免重复创建相同样式
        Map<String, CellStyle> styleCache = new HashMap<>();

        for (ExcelDataListener.DetailedValidationError error : errors) {
            try {
                // 获取错误位置
                int rowIndex = error.getRowNumber() - 1; // Excel行号从0开始，但数据行号从1开始
                Integer colIndex = fieldColumnMap.get(error.getFieldName());

                log.debug("处理错误标注 - 错误行号: {}, Excel行索引: {}, 字段: {}, 错误类型: {}",
                         error.getRowNumber(), rowIndex, error.getFieldName(), error.getErrorType());

                if (colIndex == null) {
                    log.warn("未找到字段 {} 对应的列索引，跳过标注。可用字段: {}",
                            error.getFieldName(), fieldColumnMap.keySet());
                    continue;
                }

                // 获取或创建行
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    log.warn("第{}行不存在，跳过标注", rowIndex + 1);
                    continue;
                }

                // 获取或创建单元格
                Cell cell = row.getCell(colIndex);
                if (cell == null) {
                    cell = row.createCell(colIndex);
                }

                // 应用错误标注
                applyCellAnnotation(cell, error, workbook, drawing, styleCache);

                log.debug("已标注错误 - 行: {}, 列: {}, 字段: {}, 错误: {}",
                         rowIndex + 1, colIndex, error.getFieldName(), error.getErrorType());

            } catch (Exception e) {
                log.error("标注错误失败 - 行: {}, 字段: {}, 错误: {}",
                         error.getRowNumber(), error.getFieldName(), e.getMessage());
            }
        }

        log.info("错误标注应用完成，共处理 {} 个错误", errors.size());
    }

    /**
     * 应用错误标注到单元格
     *
     * @param cell 目标单元格
     * @param error 详细验证错误
     * @param workbook Excel工作簿
     * @param drawing 绘图对象
     * @param styleCache 样式缓存
     */
    private void applyCellAnnotation(Cell cell, ExcelDataListener.DetailedValidationError error,
                                   Workbook workbook, Drawing<?> drawing,
                                   Map<String, CellStyle> styleCache) {

        // 1. 设置单元格样式（背景色和字体）
        String errorType = error.getErrorType();
        CellStyle errorStyle = styleCache.computeIfAbsent(errorType,
                type -> CellAnnotationHelper.createErrorCellStyle(workbook, type));
        cell.setCellStyle(errorStyle);

        // 2. 添加错误注释
        CellAnnotationHelper.addErrorComment(cell, error, drawing);
    }

    /**
     * 为SHP文件生成Excel错误标注文件
     *
     * @param shpEntities SHP实体数据列表
     * @param errors 验证错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param template 模板配置
     * @return 生成的Excel文件路径
     */
    @Override
    public String generateShpErrorExcel(List<GeoFeatureEntity> shpEntities,
                                      List<ExcelDataListener.DetailedValidationError> errors,
                                      Long taskId, String taskName, GisManageTemplate template) {
        try {
            log.info("开始为SHP文件生成Excel错误标注文件 - 实体数: {}, 错误数: {}",
                     shpEntities.size(), errors.size());
            // 1. 确保输出目录存在
            Path dirPath = Paths.get(ANNOTATED_FILE_DIR);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
            }

            // 2. 生成输出文件路径
            String outputFilePath = generateShpOutputFilePath(taskId, taskName);

            // 3. 创建新的Excel工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {

                // 4. 创建工作表
                Sheet sheet = workbook.createSheet("SHP数据验证结果");

                // 5. 创建绘图对象用于添加注释
                Drawing<?> drawing = sheet.createDrawingPatriarch();

                // 6. 构建字段列表（基于模板配置）
                List<String> fieldNames = buildShpFieldList(template);

                // 7. 创建表头
                createShpExcelHeader(sheet, workbook, fieldNames);

                // 8. 填充数据并标注错误
                fillShpDataWithAnnotations(sheet, workbook, drawing, shpEntities, errors, fieldNames, template);

                // 9. 自动调整列宽
                autoSizeColumns(sheet, fieldNames.size());

                // 10. 保存文件
                try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                    workbook.write(fos);
                }
            }

            log.info("SHP文件Excel错误标注文件生成成功: {}", outputFilePath);
            return outputFilePath;

        } catch (Exception e) {
            log.error("生成SHP文件Excel错误标注文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成SHP错误标注文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成SHP输出文件路径
     */
    private String generateShpOutputFilePath(Long taskId, String taskName) {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        String fileName = String.format("SHP错误标注_%s_任务%d_%s.xlsx",
                taskName != null ? taskName : "未命名", taskId, timestamp);
        return ANNOTATED_FILE_DIR + fileName;
    }

    /**
     * 构建SHP字段列表（基于模板配置）
     */
    private List<String> buildShpFieldList(GisManageTemplate template) {
        List<String> fieldNames = new ArrayList<>();

        // 添加featureId作为第一列
        fieldNames.add("featureId");

        // 从模板配置中获取字段映射
        List<Map<String, Object>> fieldMappings = template.getMap();
        log.info("构建SHP字段列表 - 模板字段映射数量: {}", fieldMappings != null ? fieldMappings.size() : 0);

        if (fieldMappings != null) {
            for (int i = 0; i < fieldMappings.size(); i++) {
                Map<String, Object> mapping = fieldMappings.get(i);
                String fieldName = (String) mapping.get("fieldName");
                String columnName = (String) mapping.get("columnName");
                String shpFieldName = (String) mapping.get("shpFieldName");
                Boolean checked = (Boolean) mapping.get("checked");

                log.info("字段映射[{}]: fieldName={}, columnName={}, shpFieldName={}, checked={}",
                         i, fieldName, columnName, shpFieldName, checked);

                // 只处理启用的字段映射
                if (fieldName != null && !fieldName.trim().isEmpty() &&
                    shpFieldName != null && !shpFieldName.trim().isEmpty() &&
                    (checked == null || checked)) {

                    // 使用shpFieldName作为表头
                    fieldNames.add(shpFieldName);
                    log.info("添加字段到表头: {} (shpFieldName)", shpFieldName);
                } else {
                    log.warn("跳过字段映射[{}]: fieldName={}, shpFieldName={}, checked={} (原因: {})",
                             i, fieldName, shpFieldName, checked,
                             (fieldName == null || fieldName.trim().isEmpty()) ? "fieldName为空" :
                             (shpFieldName == null || shpFieldName.trim().isEmpty()) ? "shpFieldName为空" : "未启用");
                }
            }
        }

        log.info("构建SHP字段列表完成，字段数: {}, 字段列表: {}", fieldNames.size(), fieldNames);
        return fieldNames;
    }

    /**
     * 创建SHP Excel表头
     */
    private void createShpExcelHeader(Sheet sheet, Workbook workbook, List<String> fieldNames) {
        Row headerRow = sheet.createRow(0);

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 设置边框
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);

        // 创建表头单元格
        for (int i = 0; i < fieldNames.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(fieldNames.get(i));
            cell.setCellStyle(headerStyle);
        }

        log.debug("SHP Excel表头创建完成，列数: {}", fieldNames.size());
    }

    /**
     * 填充SHP数据并标注错误
     */
    private void fillShpDataWithAnnotations(Sheet sheet, Workbook workbook, Drawing<?> drawing,
                                          List<GeoFeatureEntity> shpEntities,
                                          List<ExcelDataListener.DetailedValidationError> errors,
                                          List<String> fieldNames, GisManageTemplate template) {

        // 创建样式缓存，避免重复创建相同样式
        Map<String, CellStyle> styleCache = new HashMap<>();

        // 构建错误映射：实体索引 -> 字段名 -> 错误信息
        Map<Integer, Map<String, ExcelDataListener.DetailedValidationError>> errorMap = buildErrorMap(errors);

        log.info("开始填充SHP数据 - 实体数: {}, 错误映射数: {}", shpEntities.size(), errorMap.size());

        // 填充数据行
        for (int entityIndex = 0; entityIndex < shpEntities.size(); entityIndex++) {
            GeoFeatureEntity entity = shpEntities.get(entityIndex);
            int rowIndex = entityIndex + 1; // 第一行是表头

            Row dataRow = sheet.createRow(rowIndex);

            // 填充每个字段的数据
            for (int colIndex = 0; colIndex < fieldNames.size(); colIndex++) {
                String columnName = fieldNames.get(colIndex);
                Cell cell = dataRow.createCell(colIndex);

                Object fieldValue;
                String mappedFieldName;

                // 特殊处理featureId列
                if ("featureId".equals(columnName)) {
                    fieldValue = entity.getFeatureId();
                    mappedFieldName = null; // featureId不参与错误标注
                } else {
                    // 将列名映射回字段名以获取数据
                    mappedFieldName = mapColumnNameToFieldName(columnName, template);

                    // 获取字段值
                    fieldValue = getShpFieldValue(entity, mappedFieldName, entityIndex);
                }

                if (entityIndex == 0) { // 只打印第一行的详细信息
                    log.info("数据填充[行{}列{}]: 列名={}, 映射字段={}, 值={}",
                             entityIndex, colIndex, columnName, mappedFieldName, fieldValue);
                }

                // 设置单元格值
                if (fieldValue != null) {
                    cell.setCellValue(fieldValue.toString());
                } else {
                    cell.setCellValue("");
                }

                // 检查是否有错误需要标注（featureId列不参与错误标注）
                Map<String, ExcelDataListener.DetailedValidationError> entityErrors = errorMap.get(entityIndex);
                if (entityErrors != null && mappedFieldName != null) {
                    // 检查具体字段错误
                    ExcelDataListener.DetailedValidationError fieldError = entityErrors.get(mappedFieldName);

                    if (fieldError != null) {
                        // 应用错误标注
                        applyCellAnnotation(cell, fieldError, workbook, drawing, styleCache);
                        log.debug("为实体 {} 字段 {} 应用错误标注", entityIndex, columnName);
                    }
                }
            }

        }

        log.info("SHP数据填充完成，共处理 {} 个实体", shpEntities.size());
    }

    /**
     * 构建错误映射
     */
    private Map<Integer, Map<String, ExcelDataListener.DetailedValidationError>> buildErrorMap(
            List<ExcelDataListener.DetailedValidationError> errors) {

        Map<Integer, Map<String, ExcelDataListener.DetailedValidationError>> errorMap = new HashMap<>();

        for (ExcelDataListener.DetailedValidationError error : errors) {
            // SHP错误的行号实际上是实体索引（从1开始），需要转换为从0开始的索引
            int entityIndex = error.getRowNumber() - 1;
            String fieldName = error.getFieldName();

            errorMap.computeIfAbsent(entityIndex, k -> new HashMap<>()).put(fieldName, error);
        }

        log.debug("构建错误映射完成 - 错误实体数: {}, 总错误数: {}", errorMap.size(), errors.size());
        return errorMap;
    }

    /**
     * 获取SHP字段值
     */
    private Object getShpFieldValue(GeoFeatureEntity entity, String fieldName, int entityIndex) {
        if (fieldName == null) {
            if (entityIndex == 0) {
                log.warn("字段名为null，无法获取值");
            }
            return null;
        }

        // 从实体属性中获取字段值
        Map<String, Object> attributes = entity.getRawAttributes();
        if (attributes != null) {
            if (entityIndex == 0) {
                log.info("实体属性键列表: {}", attributes.keySet());
            }

            // 尝试多种可能的字段名格式
            Object value = attributes.get(fieldName);
            if (value == null) {
                // 尝试使用字段名的小写形式
                value = attributes.get(fieldName.toLowerCase());
            }
            if (value == null) {
                // 尝试使用字段名的大写形式
                value = attributes.get(fieldName.toUpperCase());
            }

            if (entityIndex == 0) {
                log.info("字段值查找: 字段名={}, 值={}", fieldName, value);
            }

            return value;
        } else {
            if (entityIndex == 0) {
                log.warn("实体属性为null");
            }
        }

        return null;
    }

    /**
     * 将shpFieldName映射回fieldName
     */
    private String mapColumnNameToFieldName(String shpFieldName, GisManageTemplate template) {
        // featureId列特殊处理
        if ("featureId".equals(shpFieldName)) {
            return null; // featureId不参与字段映射
        }

        List<Map<String, Object>> fieldMappings = template.getMap();
        if (fieldMappings != null) {
            for (Map<String, Object> mapping : fieldMappings) {
                String fieldName = (String) mapping.get("fieldName");
                String mappedShpFieldName = (String) mapping.get("shpFieldName");
                Boolean checked = (Boolean) mapping.get("checked");

                if (fieldName != null && shpFieldName.equals(mappedShpFieldName) &&
                    (checked == null || checked)) {
                    return fieldName;
                }
            }
        }

        return shpFieldName; // 如果找不到映射，返回原shpFieldName
    }



    /**
     * 自动调整列宽
     */
    private void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
            // 设置最大列宽，避免过宽
            int currentWidth = sheet.getColumnWidth(i);
            if (currentWidth > 15000) { // 约75个字符宽度
                sheet.setColumnWidth(i, 15000);
            }
        }
    }

    // ========== 接口方法实现 ==========





    @Override
    public String generateAnnotatedFileName(String taskName, Long taskId) {
        String timestamp = LocalDateTime.now().format(FORMATTER);
        String sanitizedTaskName = taskName != null ? taskName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5_-]", "_") : "未命名任务";
        return String.format("错误标注_%s_任务%s_%s.xlsx", sanitizedTaskName, taskId, timestamp);
    }

    @Override
    public String getAnnotatedFilePath(String fileName) {
        return ANNOTATED_FILE_DIR + fileName;
    }

}
