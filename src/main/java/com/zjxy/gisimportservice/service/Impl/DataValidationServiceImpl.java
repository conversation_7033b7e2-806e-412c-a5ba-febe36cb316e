package com.zjxy.gisimportservice.service.Impl;


import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisImportTask;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.entity.ValidationResult;
import com.zjxy.gisimportservice.service.DataValidationService;
import com.zjxy.gisimportservice.service.ValidationErrorExportService;
import com.zjxy.gisimportservice.util.SpringContextHolder;
import com.zjxy.gisimportservice.util.TemplateFieldMappingUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.FileDataStore;
import org.geotools.data.FileDataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.opengis.feature.Property;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 数据验证服务实现类
 *
 */
@Slf4j
@Service
public class DataValidationServiceImpl implements DataValidationService {

    @Autowired
    private ValidationErrorExportService validationErrorExportService;

    @Autowired
    private TemplateFieldMappingUtil fieldMappingUtil;


    @Value("${gis.import.task.upload.temp-path:/temp/gis-uploads}")
    private String tempPath;



    // 验证进度缓存 - 使用字符串键避免ID冲突，内部Map使用ConcurrentHashMap保证线程安全
    private final Map<String, ConcurrentHashMap<String, Object>> validationProgressCache = new ConcurrentHashMap<>();


    /**
     * 生成缓存键，避免临时任务与真实任务ID冲突
     */
    private String generateCacheKey(Long taskId, boolean isTemporary) {
        if (isTemporary) {
            return "TEMP_" + taskId + "_" + System.nanoTime();
        } else {
            return "TASK_" + taskId;
        }
    }

    /**
     * 生成缓存键（自动判断是否为临时任务）
     */
    private String generateCacheKey(Long taskId) {
        // 如果taskId是时间戳（大于某个阈值），认为是临时任务
        long threshold = 1000000000000L; // 2001年的时间戳
        boolean isTemporary = taskId > threshold;
        return generateCacheKey(taskId, isTemporary);
    }

    @Override
    public ValidationResult validateShapefileData(String filePath, GisManageTemplate template, GisImportTask task) {
        DynamicDataSourceManager.build().useDataSource("slave");
        log.info("开始验证Shapefile数据 - 文件: {}, 模板: {}", filePath, template.getNameZh());

        ValidationResult result = new ValidationResult();
        result.setStartTime(Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));
        result.setErrors(new ArrayList<>());
        result.setErrorStatistics(new HashMap<>());

        try {
            // 1. 读取Shapefile数据
            List<SimpleFeature> features;
            SimpleFeatureType schema;

            // 直接从文件路径读取文件
            String actualFilePath = task.getFilePath();
            if (actualFilePath == null || actualFilePath.trim().isEmpty()) {
                throw new IOException("任务文件路径为空，任务ID: " + task.getId());
            }

            // 检查文件是否存在
            File file = new File(actualFilePath);
            if (!file.exists()) {
                throw new IOException("文件不存在: " + actualFilePath + "，任务ID: " + task.getId());
            }

            // 处理ZIP文件，使用模板中的工作表名称
            log.info("开始从ZIP文件读取要素: {}", actualFilePath);
            log.info("模板信息: ID={}, 名称={}, 工作表={}",
                    template.getId(), template.getNameZh(), template.getSheetName());

            Map<String, Object> zipResult = readFeaturesFromZip(actualFilePath, template);
            features = (List<SimpleFeature>) zipResult.get("features");
            schema = (SimpleFeatureType) zipResult.get("schema");

            log.info("ZIP文件读取结果: features={}, schema={}",
                    features != null ? features.size() : "null",
                    schema != null ? schema.getTypeName() : "null");

            // 保存SHP文件路径到验证结果中
            String shpFilePath = (String) zipResult.get("shpFilePath");
            if (shpFilePath != null) {
                result.setShpFilePath(shpFilePath);
                log.info("SHP文件路径已保存: {}", shpFilePath);
            }

            result.setTotalRecords(features.size());
            log.info("读取到 {} 条要素数据", features.size());

            // 2. 获取字段映射配置（Shapefile不需要headerMapping）
            Map<String, String> fieldMapping = fieldMappingUtil.extractFieldMapping(template, null);
            Map<String, String> fieldTypeMapping = fieldMappingUtil.extractDbFieldTypeMapping(template);

            // 3. 初始化进度跟踪（检查taskId是否为null）
            Long taskId = task.getId();
            if (taskId != null) {
                initializeValidationProgress(taskId, features.size());
            } else {
                log.warn("任务ID为null，跳过进度跟踪初始化");
            }

            // 4. 使用统一的模板验证逻辑
            List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors = new ArrayList<>();
            int processedCount = 0;
            int errorCount = 0;
            Set<Integer> recordsWithErrors = new HashSet<>();

            log.info("开始SHP统一模板验证 - 总要素数: {}, 模板ID: {}", features.size(), template.getId());

            for (int i = 0; i < features.size(); i++) {
                SimpleFeature feature = features.get(i);
                int rowIndex = i + 1; // 行号从1开始

                // 使用统一的模板验证方法（SHP版本，传递字段映射）
                boolean isValid = validateShpDataWithTemplate(feature, template, fieldMapping, rowIndex, detailedErrors);

                if (!isValid) {
                    errorCount++;
                    recordsWithErrors.add(i);
                }

                processedCount++;

                // 每100条记录更新一次进度
                if (processedCount % 100 == 0 && taskId != null) {
                    updateValidationProgress(taskId, processedCount, features.size(), errorCount);
                }

                if (processedCount % 100 == 0) {
                    log.debug("SHP验证进度: {}/{}, 错误数: {}, 记录错误数: {}",
                            processedCount, features.size(), errorCount, recordsWithErrors.size());
                }
            }

            // 最终进度更新
            if (taskId != null) {
                updateValidationProgress(taskId, processedCount, features.size(), errorCount);
            }

            log.info("SHP统一模板验证完成 - 处理: {}, 错误: {}, 详细错误数: {}",
                     processedCount, errorCount, detailedErrors.size());

            // 5. 计算验证结果
            result.setTotalRecords(features.size());
            result.setValidRecords(features.size() - recordsWithErrors.size());
            result.setErrorRecords(detailedErrors.size()); // 详细错误总数
            result.setRecordsWithErrors(recordsWithErrors.size()); // 有错误的记录数
            result.setTotalFields(fieldMapping.size()); // 验证的字段数量

            // 6. 为SHP文件生成Excel错误标注文件
            if (!detailedErrors.isEmpty()) {
                try {
                    String taskName = template.getNameZh() != null ? template.getNameZh() : "SHP验证任务";

                    // 将SimpleFeature转换为GeoFeatureEntity
                    List<GeoFeatureEntity> geoEntities = convertSimpleFeaturesToGeoEntities(features, template);

                    // 生成SHP Excel错误标注文件
                    String errorFilePath = validationErrorExportService.exportShpValidationErrors(
                            geoEntities, detailedErrors, taskId, taskName, template);

                    result.setErrorFilePath(errorFilePath);
                    log.info("SHP验证Excel错误标注文件已生成: {}, 错误数量: {}", errorFilePath, detailedErrors.size());
                } catch (Exception e) {
                    log.error("生成SHP验证Excel错误标注文件失败: {}", e.getMessage(), e);
                    // 错误标注文件生成失败不影响验证结果
                }
            }

            // 计算多种错误率
            calculateErrorRates(result);
            result.setPassed(result.isPassed());

            // 6. 统计错误类型
            calculateErrorStatistics(result);

            log.info("Shapefile数据验证完成 - 总数: {}, 错误: {}, 错误率: {}%",
                    features.size(), errorCount, result.getErrorRate());

        } catch (Exception e) {
            log.error("Shapefile数据验证失败", e);
            result.setPassed(false);
            result.setSummary("验证过程异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 计算错误统计
     */
    private void calculateErrorStatistics(ValidationResult result) {
        Map<String, Integer> statistics = result.getErrorStatistics();

        for (ValidationResult.ValidationError error : result.getErrors()) {
            String errorType = error.getErrorType().getDescription();
            statistics.put(errorType, statistics.getOrDefault(errorType, 0) + 1);
        }
    }

    /**
     * 初始化验证进度
     */
    private void initializeValidationProgress(Long taskId, int totalRecords) {
        if (taskId == null) {
            log.warn("任务ID为null，无法初始化验证进度");
            return;
        }

        DynamicDataSourceManager.build().useDataSource("slave");
        String cacheKey = generateCacheKey(taskId);
        ConcurrentHashMap<String, Object> progress = new ConcurrentHashMap<>();
        progress.put("taskId", taskId);
        progress.put("totalRecords", totalRecords);
        progress.put("processedRecords", 0);
        progress.put("errorRecords", 0);
        progress.put("progress", 0.0);
        progress.put("status", "VALIDATING");
        progress.put("startTime", Timestamp.valueOf(LocalDateTime.now().withSecond(0).withNano(0)));

        validationProgressCache.put(cacheKey, progress);
        log.debug("初始化验证进度 - 缓存键: {}, 任务ID: {}, 总记录数: {}", cacheKey, taskId, totalRecords);
    }

    /**
     * 更新验证进度
     */
    private void updateValidationProgress(Long taskId, int processedRecords, int totalRecords, int errorRecords) {
        if (taskId == null) {
            log.debug("任务ID为null，跳过进度更新");
            return;
        }

        DynamicDataSourceManager.build().useDataSource("slave");
        String cacheKey = generateCacheKey(taskId);
        ConcurrentHashMap<String, Object> progress = validationProgressCache.get(cacheKey);
        if (progress != null) {
            progress.put("processedRecords", processedRecords);
            progress.put("errorRecords", errorRecords);
            progress.put("progress", totalRecords > 0 ? (processedRecords * 100.0) / totalRecords : 0.0);
            progress.put("lastUpdateTime", LocalDateTime.now());
            log.debug("更新验证进度 - 缓存键: {}, 任务ID: {}, 进度: {}/{}", cacheKey, taskId, processedRecords, totalRecords);
        } else {
            log.debug("未找到任务进度缓存 - 缓存键: {}, 任务ID: {}", cacheKey, taskId);
        }
    }

    /**
     * 定期清理过期的缓存项
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanupExpiredCache() {
        long currentTime = System.currentTimeMillis();
        long expireThreshold = 30 * 60 * 1000; // 30分钟过期

        validationProgressCache.entrySet().removeIf(entry -> {
            ConcurrentHashMap<String, Object> progress = entry.getValue();
            Timestamp startTime = (Timestamp) progress.get("startTime");
            if (startTime != null) {
                long age = currentTime - startTime.getTime();
                return age > expireThreshold;
            }
            return false;
        });

        log.debug("定期清理过期缓存完成，当前缓存大小: {}", validationProgressCache.size());
    }

    /**
     * 从ZIP文件中读取Shapefile要素（重载方法，支持模板参数）
     */
    private Map<String, Object> readFeaturesFromZip(String zipFilePath, GisManageTemplate template) throws IOException {
        Map<String, Object> result = new HashMap<>();
        List<SimpleFeature> features = new ArrayList<>();
        SimpleFeatureType schema = null;

        // 检查ZIP文件是否存在
        File zipFile = new File(zipFilePath);
        if (!zipFile.exists()) {
            throw new IOException("ZIP文件不存在: " + zipFilePath);
        }

        // 创建临时目录 - 使用配置的临时路径
        Path tempDir = createConfiguredTempDirectory("shapefile_validation_");
        try {
            log.info("解压ZIP文件到临时目录: {}", tempDir);

            // 解压ZIP文件
            unzipFile(zipFilePath, tempDir.toFile());

            // 列出解压后的所有文件
            listDirectoryContents(tempDir.toFile(), "解压后的文件");

            // 根据模板的工作表名称查找SHP文件
            String sheetName = template != null ? template.getSheetName() : null;
            File shpFile;

            shpFile = findShpFile(tempDir.toFile(), sheetName);


            if (shpFile == null) {
                throw new IOException("在ZIP文件中未找到.shp文件: " + zipFilePath);
            }

            log.info("找到SHP文件: {}", shpFile.getAbsolutePath());

            // 读取Shapefile - 改进版本，支持中文文件名和更好的错误处理
            features = readShapefileWithEnhancedSupport(shpFile);
            schema = getShapefileSchema(shpFile);

            log.info("成功读取 {} 条要素数据", features.size());

            // 如果读取到0条数据，进行详细诊断
            if (features.isEmpty()) {
                performShapefileDiagnostics(shpFile);
            }

            // 将找到的SHP文件路径保存到结果中，供后续处理使用
            result.put("shpFilePath", shpFile.getAbsolutePath());

        } finally {
            // 清理临时目录
            try {
                Files.walk(tempDir)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);
                log.info("清理临时目录: {}", tempDir);
            } catch (IOException e) {
                log.warn("清理临时目录失败: {}", tempDir, e);
            }
        }

        result.put("features", features);
        result.put("schema", schema);
        return result;
    }

    /**
     * 解压ZIP文件（保留原方法用于兼容）
     */
    private void unzipFile(String zipFilePath, File destDir) throws IOException {
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath), Charset.forName("GBK"))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File file = new File(destDir, entry.getName());

                // 确保父目录存在
                File parent = file.getParentFile();
                if (parent != null && !parent.exists()) {
                    parent.mkdirs();
                }

                if (!entry.isDirectory()) {
                    try (FileOutputStream fos = new FileOutputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, length);
                        }
                    }
                }
                zis.closeEntry();
            }
        }
    }

    /**
     * 根据工作表名称查找SHP文件
     */
    private File findShpFile(File directory, String sheetName) {
        if (sheetName == null || sheetName.trim().isEmpty()) {
            log.warn("工作表名称为空，使用默认查找逻辑");
            return null;
        }

        final String target = sheetName.trim();
        log.info("根据工作表名称查找SHP文件: {}", target);

        File[] files = directory.listFiles((dir, name) -> {
            String low = name.toLowerCase();
            return low.endsWith(".shp") &&
                    low.substring(0, low.length())
                            .equals(target.toLowerCase());
        });

        if (files != null && files.length > 0) {
            log.info("找到匹配的SHP文件: {}", files[0].getName());
            return files[0];
        }
        return null;
    }

    /**
     * 计算多种错误率
     */
    private void calculateErrorRates(ValidationResult result) {
        int totalRecords = result.getTotalRecords();
        int errorRecords = result.getErrorRecords(); // 字段级错误总数
        int recordsWithErrors = result.getRecordsWithErrors(); // 有错误的记录数
        int totalFields = result.getTotalFields();

        if (totalRecords > 0) {
            // 原有的错误率（字段级错误率）
            result.setErrorRate((double) errorRecords / totalRecords * 100);

            // 记录错误率（有错误记录数 / 总记录数）
            result.setRecordErrorRate((double) recordsWithErrors / totalRecords * 100);

            // 字段错误率（总错误数 / (总记录数 × 字段数量)）
            if (totalFields > 0) {
                result.setFieldErrorRate((double) errorRecords / (totalRecords * totalFields) * 100);
            } else {
                result.setFieldErrorRate(0.0);
            }
        } else {
            result.setErrorRate(0.0);
            result.setRecordErrorRate(0.0);
            result.setFieldErrorRate(0.0);
        }

        log.info("错误率统计 - 字段级错误率: {:.2f}%, 记录错误率: {:.2f}%, 字段错误率: {:.2f}%",
                result.getErrorRate(), result.getRecordErrorRate(), result.getFieldErrorRate());
    }

    /**
     * 创建配置的临时目录
     */
    private Path createConfiguredTempDirectory(String prefix) throws IOException {
        // 确保配置的临时目录存在
        File tempBaseDir = new File(tempPath);
        if (!tempBaseDir.exists()) {
            boolean created = tempBaseDir.mkdirs();
            if (!created) {
                log.warn("无法创建配置的临时目录: {}, 使用系统默认临时目录", tempPath);
                return Files.createTempDirectory(prefix);
            }
        }

        // 在配置的目录下创建临时子目录
        Path tempDir = Files.createTempDirectory(tempBaseDir.toPath(), prefix);
        log.info("使用配置的临时目录: {}", tempDir);
        return tempDir;
    }

    /**
     * 增强的Shapefile读取方法，支持中文文件名和更好的错误处理
     */
    private List<SimpleFeature> readShapefileWithEnhancedSupport(File shpFile) throws IOException {
        List<SimpleFeature> features = new ArrayList<>();

        log.info("开始读取Shapefile: {}", shpFile.getAbsolutePath());
        log.info("文件大小: {} bytes", shpFile.length());

        // 检查相关文件是否存在
        checkShapefileCompleteness(shpFile);

        // 尝试多种编码方式读取
        String[] encodings = {"GBK", "UTF-8", "GB2312", "ISO-8859-1"};

        for (String encoding : encodings) {
            try {
                features = tryReadWithEncoding(shpFile, encoding);
                if (!features.isEmpty()) {
                    log.info("使用编码 {} 成功读取到 {} 条要素", encoding, features.size());
                    break;
                } else {
                    log.debug("使用编码 {} 读取到0条要素", encoding);
                }
            } catch (Exception e) {
                log.debug("使用编码 {} 读取失败: {}", encoding, e.getMessage());
            }
        }

        return features;
    }

    /**
     * 使用指定编码尝试读取Shapefile
     */
    private List<SimpleFeature> tryReadWithEncoding(File shpFile, String encoding) throws IOException {
        List<SimpleFeature> features = new ArrayList<>();

        // 方法1: 使用FileDataStoreFinder
        try {
            FileDataStore dataStore = FileDataStoreFinder.getDataStore(shpFile);
            if (dataStore != null) {
                try {
                    // 设置编码
                    if (dataStore instanceof org.geotools.data.shapefile.ShapefileDataStore) {
                        ((org.geotools.data.shapefile.ShapefileDataStore) dataStore).setCharset(Charset.forName(encoding));
                    }

                    SimpleFeatureSource featureSource = dataStore.getFeatureSource();
                    SimpleFeatureCollection featureCollection = featureSource.getFeatures();

                    try (SimpleFeatureIterator iterator = featureCollection.features()) {
                        while (iterator.hasNext()) {
                            features.add(iterator.next());
                        }
                    }

                    log.debug("方法1成功读取 {} 条要素 (编码: {})", features.size(), encoding);
                    return features;
                } finally {
                    dataStore.dispose();
                }
            }
        } catch (Exception e) {
            log.debug("方法1读取失败 (编码: {}): {}", encoding, e.getMessage());
        }

        // 方法2: 使用DataStoreFinder
        if (features.isEmpty()) {
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("url", shpFile.toURI().toURL());
                params.put("charset", encoding);

                DataStore dataStore = DataStoreFinder.getDataStore(params);
                if (dataStore != null) {
                    try {
                        String typeName = dataStore.getTypeNames()[0];
                        SimpleFeatureSource source = dataStore.getFeatureSource(typeName);
                        SimpleFeatureCollection collection = source.getFeatures();

                        try (SimpleFeatureIterator iterator = collection.features()) {
                            while (iterator.hasNext()) {
                                features.add(iterator.next());
                            }
                        }

                        log.debug("方法2成功读取 {} 条要素 (编码: {})", features.size(), encoding);
                    } finally {
                        dataStore.dispose();
                    }
                }
            } catch (Exception e) {
                log.debug("方法2读取失败 (编码: {}): {}", encoding, e.getMessage());
            }
        }

        return features;
    }

    /**
     * 获取Shapefile的Schema
     */
    private SimpleFeatureType getShapefileSchema(File shpFile) throws IOException {
        try {
            FileDataStore dataStore = FileDataStoreFinder.getDataStore(shpFile);
            if (dataStore != null) {
                try {
                    SimpleFeatureSource featureSource = dataStore.getFeatureSource();
                    return featureSource.getSchema();
                } finally {
                    dataStore.dispose();
                }
            }
        } catch (Exception e) {
            log.warn("获取Schema失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 检查Shapefile文件完整性
     */
    private void checkShapefileCompleteness(File shpFile) throws IOException {
        String baseName = shpFile.getName().substring(0, shpFile.getName().lastIndexOf('.'));
        File parentDir = shpFile.getParentFile();

        // 检查必需的文件
        String[] requiredExtensions = {".shp", ".shx", ".dbf"};
        String[] optionalExtensions = {".prj", ".cpg"};

        log.info("检查Shapefile文件完整性:");

        for (String ext : requiredExtensions) {
            File requiredFile = new File(parentDir, baseName + ext);
            if (requiredFile.exists()) {
                log.info("  ✓ {} 存在 (大小: {} bytes)", requiredFile.getName(), requiredFile.length());
            } else {
                throw new IOException("缺少必需的文件: " + requiredFile.getName());
            }
        }

        for (String ext : optionalExtensions) {
            File optionalFile = new File(parentDir, baseName + ext);
            if (optionalFile.exists()) {
                log.info("  ✓ {} 存在 (大小: {} bytes)", optionalFile.getName(), optionalFile.length());
            } else {
                log.info("  - {} 不存在 (可选文件)", baseName + ext);
            }
        }
    }

    /**
     * 执行Shapefile诊断
     */
    private void performShapefileDiagnostics(File shpFile) {
        log.warn("=== Shapefile诊断信息 ===");
        log.warn("文件路径: {}", shpFile.getAbsolutePath());
        log.warn("文件名: {}", shpFile.getName());
        log.warn("文件大小: {} bytes", shpFile.length());
        log.warn("文件是否可读: {}", shpFile.canRead());

        // 检查文件内容
        try {
            byte[] header = new byte[100];
            try (FileInputStream fis = new FileInputStream(shpFile)) {
                int bytesRead = fis.read(header);
                log.warn("读取到文件头 {} 字节", bytesRead);

                // 检查Shapefile魔数
                if (bytesRead >= 4) {
                    int magic = ((header[0] & 0xFF) << 24) | ((header[1] & 0xFF) << 16) |
                               ((header[2] & 0xFF) << 8) | (header[3] & 0xFF);
                    log.warn("文件魔数: 0x{} (期望: 0x0000270A)", Integer.toHexString(magic));

                    if (magic == 0x0000270A) {
                        log.warn("文件魔数正确，这是一个有效的Shapefile");
                    } else {
                        log.warn("文件魔数不正确，可能不是有效的Shapefile");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("读取文件头失败: {}", e.getMessage());
        }

        log.warn("=== 诊断结束 ===");
    }

    /**
     * 列出目录内容用于调试
     */
    private void listDirectoryContents(File directory, String description) {
        log.info("=== {} ===", description);
        log.info("目录路径: {}", directory.getAbsolutePath());

        if (!directory.exists()) {
            log.warn("目录不存在: {}", directory.getAbsolutePath());
            return;
        }

        File[] files = directory.listFiles();
        if (files == null || files.length == 0) {
            log.warn("目录为空: {}", directory.getAbsolutePath());
            return;
        }

        log.info("目录包含 {} 个文件/文件夹:", files.length);
        for (File file : files) {
            if (file.isDirectory()) {
                log.info("  [目录] {}", file.getName());
                // 递归列出子目录内容
                listDirectoryContents(file, "子目录: " + file.getName());
            } else {
                log.info("  [文件] {} (大小: {} bytes)", file.getName(), file.length());
            }
        }
        log.info("=== {} 结束 ===", description);
    }



    @Override
    public boolean validateDataWithTemplate(Object data, GisManageTemplate template, int rowIndex,
                                          List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors) {
        try {
            log.debug("开始统一模板验证 - 模板ID: {}, 行号: {}", template.getId(), rowIndex);

            // 1. 获取验证配置
            com.zjxy.gisimportservice.entity.GisManageTemplateValid validConfig = getValidationConfig(template.getId());
            if (validConfig == null) {
                log.warn("模板 {} 没有找到验证配置，跳过验证", template.getId());
                return true;
            }

            if (validConfig.getSx() == null) {
                log.warn("模板 {} 的验证配置中sx字段为null，跳过验证", template.getId());
                return true;
            }

            if (validConfig.getSx().isEmpty()) {
                log.warn("模板 {} 的验证配置中sx字段为空列表，跳过验证", template.getId());
                return true;
            }

            log.info("模板 {} 找到验证配置，sx规则数量: {}", template.getId(), validConfig.getSx().size());

            // 2. 遍历验证规则
            boolean dataValid = true;
            int enabledRuleCount = 0;
            for (Map<String, Object> rule : validConfig.getSx()) {
                String fieldName = (String) rule.get("fieldName");
                Boolean checkedSX = (Boolean) rule.get("checkedSX");

                log.debug("检查验证规则 - 字段: {}, 启用状态: {}, 规则内容: {}", fieldName, checkedSX, rule);

                // 跳过未启用的验证规则
                if (fieldName == null || checkedSX == null || !checkedSX) {
                    continue;
                }

                enabledRuleCount++;

                // 获取字段值
                Object fieldValue = extractFieldValue(data, fieldName);

                // 转换验证规则为字段配置格式
                Map<String, Object> fieldConfig = convertRuleToFieldConfig(rule);

                // 使用本类的validateField方法验证字段
                boolean fieldValid = this.validateField(
                        fieldName, fieldValue, fieldConfig, rowIndex, detailedErrors);

                if (!fieldValid) {
                    dataValid = false;
                    log.debug("字段验证失败 - 字段: {}, 行: {}", fieldName, rowIndex);
                }
            }

            log.info("模板 {} 验证完成 - 总规则: {}, 启用规则: {}, 验证结果: {}",
                     template.getId(), validConfig.getSx().size(), enabledRuleCount, dataValid);

            return dataValid;

        } catch (Exception e) {
            log.error("统一模板验证异常 - 模板ID: {}, 行号: {}", template.getId(), rowIndex, e);

            // 收集异常错误
            Map<Integer, Object> rowData = createRowDataMap(data);
            this.collectDetailedError(rowIndex,
                    "模板验证异常: " + e.getMessage(), "TEMPLATE_VALIDATION_EXCEPTION",
                    rowData, null, null, detailedErrors);

            return false;
        }
    }

    /**
     * SHP数据的统一模板验证方法（支持字段映射）
     */
    private boolean validateShpDataWithTemplate(org.opengis.feature.simple.SimpleFeature feature,
                                              GisManageTemplate template,
                                              Map<String, String> fieldMapping,
                                              int rowIndex,
                                              List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors) {
        try {
            log.debug("开始SHP统一模板验证 - 模板ID: {}, 行号: {}", template.getId(), rowIndex);

            // 1. 获取验证配置
            com.zjxy.gisimportservice.entity.GisManageTemplateValid validConfig = getValidationConfig(template.getId());
            if (validConfig == null) {
                log.warn("模板 {} 没有找到验证配置，跳过验证", template.getId());
                return true;
            }

            if (validConfig.getSx() == null) {
                log.warn("模板 {} 的验证配置中sx字段为null，跳过验证", template.getId());
                return true;
            }

            if (validConfig.getSx().isEmpty()) {
                log.warn("模板 {} 的验证配置中sx字段为空列表，跳过验证", template.getId());
                return true;
            }

            log.debug("模板 {} 找到验证配置，sx规则数量: {}", template.getId(), validConfig.getSx().size());

            // 2. 遍历验证规则
            boolean dataValid = true;
            int enabledRuleCount = 0;
            for (Map<String, Object> rule : validConfig.getSx()) {
                String fieldName = (String) rule.get("fieldName");
                Boolean checkedSX = (Boolean) rule.get("checkedSX");

                log.debug("检查SHP验证规则 - 字段: {}, 启用状态: {}, 规则内容: {}", fieldName, checkedSX, rule);

                // 跳过未启用的验证规则
                if (fieldName == null || checkedSX == null || !checkedSX) {
                    log.debug("跳过SHP验证规则 - 字段: {}, 原因: {}", fieldName,
                             fieldName == null ? "字段名为null" :
                             checkedSX == null ? "启用状态为null" : "未启用");
                    continue;
                }

                enabledRuleCount++;

                // 获取SHP字段名（从字段映射或规则中获取）
                String shpFieldName = getShpFieldName(fieldName, rule, fieldMapping);
                log.debug("字段映射 - 模板字段: {} -> SHP字段: {}", fieldName, shpFieldName);

                // 获取字段值（使用SHP字段名）
                Object fieldValue = feature.getAttribute(shpFieldName);
                log.debug("提取字段值 - SHP字段: {}, 值: {}", shpFieldName, fieldValue);

                // 转换验证规则为字段配置格式
                Map<String, Object> fieldConfig = convertRuleToFieldConfig(rule);

                // 使用本类的validateField方法验证字段
                boolean fieldValid = this.validateField(
                        fieldName, fieldValue, fieldConfig, rowIndex, detailedErrors);

                if (!fieldValid) {
                    dataValid = false;
                    log.debug("SHP字段验证失败 - 字段: {}, 行: {}", fieldName, rowIndex);
                }
            }

            log.info("模板 {} SHP验证完成 - 总规则: {}, 启用规则: {}, 验证结果: {}",
                     template.getId(), validConfig.getSx().size(), enabledRuleCount, dataValid);

            return dataValid;

        } catch (Exception e) {
            log.error("SHP统一模板验证异常 - 模板ID: {}, 行号: {}", template.getId(), rowIndex, e);

            // 收集异常错误
            Map<Integer, Object> rowData = createShpRowDataMap(feature);
            this.collectDetailedError(rowIndex,
                    "SHP模板验证异常: " + e.getMessage(), "SHP_TEMPLATE_VALIDATION_EXCEPTION",
                    rowData, null, null, detailedErrors);

            return false;
        }
    }

    /**
     * 获取SHP字段名
     */
    private String getShpFieldName(String templateFieldName, Map<String, Object> rule, Map<String, String> fieldMapping) {
        // 1. 优先从规则中获取shpFieldName
        String shpFieldName = (String) rule.get("shpFieldName");
        if (shpFieldName != null && !shpFieldName.trim().isEmpty()) {
            return shpFieldName;
        }

        // 2. 从字段映射中获取（反向查找）
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            if (templateFieldName.equals(entry.getValue())) {
                return entry.getKey();
            }
        }

        // 3. 如果都没有，使用模板字段名
        log.warn("未找到字段 {} 的SHP映射，使用模板字段名", templateFieldName);
        return templateFieldName;
    }

    /**
     * 创建SHP行数据映射（用于错误收集）
     */
    private Map<Integer, Object> createShpRowDataMap(org.opengis.feature.simple.SimpleFeature feature) {
        Map<Integer, Object> rowData = new HashMap<>();

        try {
            int index = 0;
            for (org.opengis.feature.Property property : feature.getProperties()) {
                String name = property.getName().getLocalPart();
                Object value = property.getValue();
                // 存储字段名和值的映射，便于后续提取
                rowData.put(index++, "FIELD:" + name + "=" + (value != null ? value.toString() : ""));
            }
        } catch (Exception e) {
            log.warn("创建SHP行数据映射失败: {}", e.getMessage());
            rowData.put(0, "SHP Feature ID: " + feature.getID());
        }

        return rowData;
    }

    @Override
    public com.zjxy.gisimportservice.entity.GisManageTemplateValid getValidationConfig(Integer templateId) {
        try {
            com.zjxy.gisimportservice.service.GisManageTemplateValidService templateValidService =
                    SpringContextHolder.getBean(com.zjxy.gisimportservice.service.GisManageTemplateValidService.class);

            if (templateValidService != null) {
                return templateValidService.getValidByTemplateId(templateId);
            }

            log.warn("无法获取GisManageTemplateValidService");
            return null;

        } catch (Exception e) {
            log.error("获取验证配置失败 - 模板ID: {}", templateId, e);
            return null;
        }
    }

    /**
     * 从数据对象中提取字段值
     */
    private Object extractFieldValue(Object data, String fieldName) {
        if (data instanceof com.zjxy.gisimportservice.entity.GeoFeatureEntity) {
            return ((com.zjxy.gisimportservice.entity.GeoFeatureEntity) data).getAttribute(fieldName);
        } else if (data instanceof org.opengis.feature.simple.SimpleFeature) {
            return ((org.opengis.feature.simple.SimpleFeature) data).getAttribute(fieldName);
        } else if (data instanceof Map) {
            return ((Map<?, ?>) data).get(fieldName);
        }

        log.warn("不支持的数据类型: {}", data.getClass().getSimpleName());
        return null;
    }

    /**
     * 转换验证规则为字段配置格式
     */
    private Map<String, Object> convertRuleToFieldConfig(Map<String, Object> rule) {
        Map<String, Object> fieldConfig = new HashMap<>();

        String checkType = (String) rule.get("checkType");
        log.debug("转换验证规则 - checkType: {}, 完整规则: {}", checkType, rule);

        // 1. 必填字段验证
        if ("emptyValue".equals(checkType)) {
            fieldConfig.put("required", true);
        }

        // 2. 枚举值验证
        else if ("enums".equals(checkType)) {
            fieldConfig.put("validationType", "enums");
            Object enumsCheckVal = rule.get("enumsCheckVal");
            if (enumsCheckVal != null) {
                fieldConfig.put("allowedValues", enumsCheckVal.toString().split(","));
                log.debug("设置枚举验证 - 允许的值: {}", enumsCheckVal);
            }
        }

        // 3. 唯一值验证
        else if ("onlyValue".equals(checkType)) {
            fieldConfig.put("validationType", "onlyValue");
            Object onlyCheckVal = rule.get("onlyCheckVal");
            if (onlyCheckVal != null) {
                fieldConfig.put("onlyAllowedValue", onlyCheckVal.toString());
                log.debug("设置唯一值验证 - 唯一允许值: {}", onlyCheckVal);
            }
        }

        // 4. 数值范围验证
        else if ("scaleValue".equals(checkType)) {
            fieldConfig.put("validationType", "scaleValue");
            Object scaleCheckVal = rule.get("scaleCheckVal");
            if (scaleCheckVal != null) {
                String[] range = scaleCheckVal.toString().split(",");
                if (range.length == 2) {
                    try {
                        fieldConfig.put("minValue", Double.parseDouble(range[0].trim()));
                        fieldConfig.put("maxValue", Double.parseDouble(range[1].trim()));
                        log.debug("设置范围验证 - 最小值: {}, 最大值: {}", range[0], range[1]);
                    } catch (NumberFormatException e) {
                        log.warn("范围验证配置格式错误: {}", scaleCheckVal);
                    }
                }
            }
        }

        // 5. 数据类型配置
        String dataTypeSX = (String) rule.get("dataTypeSX");
        if (dataTypeSX != null) {
            fieldConfig.put("dataType", dataTypeSX);
        }

        log.debug("转换后的字段配置: {}", fieldConfig);
        return fieldConfig;
    }

    /**
     * 创建行数据映射（用于错误收集）
     */
    private Map<Integer, Object> createRowDataMap(Object data) {
        Map<Integer, Object> rowData = new HashMap<>();

        if (data instanceof com.zjxy.gisimportservice.entity.GeoFeatureEntity) {
            com.zjxy.gisimportservice.entity.GeoFeatureEntity entity =
                    (com.zjxy.gisimportservice.entity.GeoFeatureEntity) data;
            Map<String, Object> attributes = entity.getRawAttributes();
            if (attributes != null) {
                int index = 0;
                for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                    rowData.put(index++, entry.getKey() + ": " +
                               (entry.getValue() != null ? entry.getValue().toString() : "null"));
                }
            }
        } else {
            rowData.put(0, data.toString());
        }

        return rowData;
    }

    @Override
    public boolean validateField(String fieldName, Object fieldValue, Map<String, Object> fieldConfig,
                               int rowIndex, List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors) {

        boolean isValid = true;

        try {
            log.debug("开始验证字段 - 字段: {}, 值: {}, 配置: {}", fieldName, fieldValue, fieldConfig);

            // 1. 检查必填字段
            Boolean isRequired = (Boolean) fieldConfig.get("required");
            if (isRequired != null && isRequired) {
                ValidationFieldResult requiredResult = validateRequiredField(fieldName, fieldValue, true);
                if (!requiredResult.isValid()) {
                    collectDetailedError(rowIndex, requiredResult.getErrorMessage(), requiredResult.getErrorType(),
                            createRowDataMapForField(fieldName, fieldValue), fieldName, fieldValue, detailedErrors);
                    isValid = false;
                    log.debug("必填字段验证失败 - 字段: {}", fieldName);
                }
            }

            // 2. 检查特定验证类型
            String validationType = (String) fieldConfig.get("validationType");
            if (validationType != null && fieldValue != null) {
                ValidationFieldResult validationResult = null;

                switch (validationType) {
                    case "enums":
                        validationResult = validateEnumValue(fieldName, fieldValue, fieldConfig);
                        break;
                    case "onlyValue":
                        validationResult = validateOnlyValue(fieldName, fieldValue, fieldConfig);
                        break;
                    case "scaleValue":
                        validationResult = validateScaleValue(fieldName, fieldValue, fieldConfig);
                        break;
                    default:
                        log.debug("未知的验证类型: {}", validationType);
                }

                if (validationResult != null && !validationResult.isValid()) {
                    collectDetailedError(rowIndex, validationResult.getErrorMessage(), validationResult.getErrorType(),
                            createRowDataMapForField(fieldName, fieldValue), fieldName, fieldValue, detailedErrors);
                    isValid = false;
                    log.warn("字段验证失败 - 字段: {}, 类型: {}, 值: {}, 错误: {}",
                             fieldName, validationType, fieldValue, validationResult.getErrorMessage());
                }
            }

            // 3. 检查数据类型
            String expectedType = (String) fieldConfig.get("dataType");
            if (expectedType != null && fieldValue != null) {
                ValidationFieldResult typeResult = validateDataType(fieldName, fieldValue, expectedType);
                if (!typeResult.isValid()) {
                    collectDetailedError(rowIndex, typeResult.getErrorMessage(), typeResult.getErrorType(),
                            createRowDataMapForField(fieldName, fieldValue), fieldName, fieldValue, detailedErrors);
                    isValid = false;
                    log.debug("数据类型验证失败 - 字段: {}", fieldName);
                }
            }

            log.debug("字段验证完成 - 字段: {}, 结果: {}", fieldName, isValid);

        } catch (Exception e) {
            log.error("字段验证异常 - 字段: {}, 值: {}, 行: {}", fieldName, fieldValue, rowIndex, e);
            collectDetailedError(rowIndex, "字段验证异常: " + e.getMessage(), "VALIDATION_EXCEPTION",
                    createRowDataMapForField(fieldName, fieldValue), fieldName, fieldValue, detailedErrors);
            isValid = false;
        }

        return isValid;
    }

    @Override
    public void collectDetailedError(int rowNumber, String errorMessage, String errorType,
                                   Map<Integer, Object> originalData, String fieldName, Object fieldValue,
                                   List<com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError> detailedErrors) {

        com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError detailedError =
                new com.zjxy.gisimportservice.listener.ExcelDataListener.DetailedValidationError(
                        rowNumber, errorMessage, errorType, originalData, fieldName, fieldValue);

        detailedErrors.add(detailedError);
        log.debug("收集详细错误 - 行号: {}, 字段: {}, 错误: {}", rowNumber, fieldName, errorMessage);
    }

    /**
     * 验证枚举值
     */
    private ValidationFieldResult validateEnumValue(String fieldName, Object fieldValue, Map<String, Object> fieldConfig) {
        String[] allowedValues = (String[]) fieldConfig.get("allowedValues");
        if (allowedValues == null || allowedValues.length == 0) {
            return new ValidationFieldResult(true);
        }

        String valueStr = fieldValue.toString();
        for (String allowedValue : allowedValues) {
            if (allowedValue.trim().equals(valueStr)) {
                log.debug("枚举验证通过 - 字段: {}, 值: {}, 允许值: {}", fieldName, valueStr, String.join(",", allowedValues));
                return new ValidationFieldResult(true);
            }
        }

        return new ValidationFieldResult(false, "ENUM_VALUE_INVALID",
                "字段 '" + fieldName + "' 的值 '" + valueStr + "' 不在允许的枚举值中，允许的值: " + String.join(",", allowedValues),
                "请选择以下允许的值之一: " + String.join(",", allowedValues));
    }

    /**
     * 验证唯一值
     */
    private ValidationFieldResult validateOnlyValue(String fieldName, Object fieldValue, Map<String, Object> fieldConfig) {
        String onlyAllowedValue = (String) fieldConfig.get("onlyAllowedValue");
        if (onlyAllowedValue == null) {
            return new ValidationFieldResult(true);
        }

        String valueStr = fieldValue.toString();
        if (onlyAllowedValue.equals(valueStr)) {
            log.debug("唯一值验证通过 - 字段: {}, 值: {}", fieldName, valueStr);
            return new ValidationFieldResult(true);
        }

        return new ValidationFieldResult(false, "ONLY_VALUE_INVALID",
                "字段 '" + fieldName + "' 的值 '" + valueStr + "' 不等于唯一允许值 '" + onlyAllowedValue + "'",
                "该字段只允许值: " + onlyAllowedValue);
    }

    /**
     * 验证数值范围
     */
    private ValidationFieldResult validateScaleValue(String fieldName, Object fieldValue, Map<String, Object> fieldConfig) {
        Double minValue = (Double) fieldConfig.get("minValue");
        Double maxValue = (Double) fieldConfig.get("maxValue");

        if (minValue == null && maxValue == null) {
            return new ValidationFieldResult(true);
        }

        try {
            double value = Double.parseDouble(fieldValue.toString());

            if (minValue != null && value < minValue) {
                return new ValidationFieldResult(false, "SCALE_VALUE_TOO_SMALL",
                        "字段 '" + fieldName + "' 的值 " + value + " 小于最小值 " + minValue,
                        "请输入大于等于 " + minValue + " 的值");
            }

            if (maxValue != null && value > maxValue) {
                return new ValidationFieldResult(false, "SCALE_VALUE_TOO_LARGE",
                        "字段 '" + fieldName + "' 的值 " + value + " 大于最大值 " + maxValue,
                        "请输入小于等于 " + maxValue + " 的值");
            }

            log.debug("范围验证通过 - 字段: {}, 值: {}, 范围: [{}, {}]", fieldName, value, minValue, maxValue);
            return new ValidationFieldResult(true);

        } catch (NumberFormatException e) {
            return new ValidationFieldResult(false, "SCALE_VALUE_NOT_NUMBER",
                    "字段 '" + fieldName + "' 的值 '" + fieldValue + "' 不是有效的数值",
                    "请输入有效的数值");
        }
    }

    /**
     * 验证必填字段
     */
    private ValidationFieldResult validateRequiredField(String fieldName, Object fieldValue, boolean isRequired) {
        if (!isRequired) {
            return new ValidationFieldResult(true);
        }

        if (fieldValue == null || fieldValue.toString().trim().isEmpty()) {
            return new ValidationFieldResult(false, "REQUIRED_FIELD",
                    "必填字段 '" + fieldName + "' 不能为空", "请填写该必填字段");
        }

        return new ValidationFieldResult(true);
    }

    /**
     * 验证数据类型
     */
    private ValidationFieldResult validateDataType(String fieldName, Object fieldValue, String expectedType) {
        if (fieldValue == null) {
            return new ValidationFieldResult(true); // null值由必填验证处理
        }

        try {
            switch (expectedType.toLowerCase()) {
                case "integer":
                case "int":
                case "bigint":
                    if (!(fieldValue instanceof Number)) {
                        try {
                            Integer.parseInt(fieldValue.toString());
                        } catch (NumberFormatException e) {
                            return new ValidationFieldResult(false, "DATA_TYPE_MISMATCH",
                                    "字段 '" + fieldName + "' 应为整数类型，当前值: " + fieldValue,
                                    "请输入有效的整数");
                        }
                    }
                    break;

                case "double":
                case "float":
                case "decimal":
                case "double precision":
                    if (!(fieldValue instanceof Number)) {
                        try {
                            Double.parseDouble(fieldValue.toString());
                        } catch (NumberFormatException e) {
                            return new ValidationFieldResult(false, "DATA_TYPE_MISMATCH",
                                    "字段 '" + fieldName + "' 应为数值类型，当前值: " + fieldValue,
                                    "请输入有效的数值");
                        }
                    }
                    break;

                case "string":
                case "varchar":
                case "character varying":
                case "text":
                    // 字符串类型通常都能转换
                    break;

                case "date":
                case "datetime":
                case "timestamp":
                    // 日期类型验证可以在这里添加
                    break;

                default:
                    log.debug("未知数据类型: {}, 跳过类型验证", expectedType);
            }

            return new ValidationFieldResult(true);

        } catch (Exception e) {
            return new ValidationFieldResult(false, "DATA_TYPE_ERROR",
                    "字段 '" + fieldName + "' 数据类型验证异常: " + e.getMessage(),
                    "请检查数据格式是否正确");
        }
    }

    /**
     * 创建行数据映射（用于字段错误收集）
     */
    private Map<Integer, Object> createRowDataMapForField(String fieldName, Object fieldValue) {
        Map<Integer, Object> rowData = new HashMap<>();
        rowData.put(0, fieldName + ": " + (fieldValue != null ? fieldValue.toString() : "null"));
        return rowData;
    }

    /**
     * 字段验证结果内部类
     */
    @Getter
    private static class ValidationFieldResult {
        // Getters
        private final boolean valid;
        private String errorType;
        private String errorMessage;
        private String suggestion;

        public ValidationFieldResult(boolean valid) {
            this.valid = valid;
        }

        public ValidationFieldResult(boolean valid, String errorType, String errorMessage, String suggestion) {
            this.valid = valid;
            this.errorType = errorType;
            this.errorMessage = errorMessage;
            this.suggestion = suggestion;
        }

    }

    /**
     * 将SimpleFeature列表转换为GeoFeatureEntity列表
     */
    private List<GeoFeatureEntity> convertSimpleFeaturesToGeoEntities(List<SimpleFeature> features, GisManageTemplate template) {
        List<GeoFeatureEntity> geoEntities = new ArrayList<>();

        log.info("开始转换SimpleFeature到GeoFeatureEntity - 输入数量: {}", features.size());

        // 调试：打印模板配置信息
        List<Map<String, Object>> fieldMappings = template.getMap();
        log.info("模板字段映射配置数量: {}", fieldMappings != null ? fieldMappings.size() : 0);
        if (fieldMappings != null) {
            for (int j = 0; j < Math.min(fieldMappings.size(), 5); j++) {
                Map<String, Object> mapping = fieldMappings.get(j);
                log.info("字段映射[{}]: fieldName={}, shpFieldName={}, columnName={}, checked={}",
                         j, mapping.get("fieldName"), mapping.get("shpFieldName"),
                         mapping.get("columnName"), mapping.get("checked"));
            }
        }

        for (int i = 0; i < features.size(); i++) {
            SimpleFeature feature = features.get(i);
            GeoFeatureEntity entity = new GeoFeatureEntity();

            // 设置基本信息 - 使用更有意义的featureId
            String featureId = feature.getID();
            if (featureId == null || featureId.trim().isEmpty()) {
                featureId = "feature_" + (i + 1);
            }
            entity.setFeatureId(featureId);
            entity.setCreatedAt(LocalDateTime.now());

            // 转换属性
            Map<String, Object> attributes = new HashMap<>();

            // 从SimpleFeature中提取属性
            for (Property property : feature.getProperties()) {
                String propertyName = property.getName().getLocalPart();
                Object value = property.getValue();

                // 处理几何属性：转换为可读的字符串格式
                if ("the_geom".equals(propertyName) || "geometry".equals(propertyName)) {
                    if (value != null) {
                        // 将几何对象转换为WKT格式的字符串
                        attributes.put(propertyName, value.toString());
                        if (i == 0) {
                            log.info("几何属性[{}]: {} = {} (类型: {})", i, propertyName,
                                     value.getClass().getSimpleName(), value.toString().substring(0, Math.min(50, value.toString().length())) + "...");
                        }
                    } else {
                        attributes.put(propertyName, "");
                    }
                } else {
                    // 普通属性
                    attributes.put(propertyName, value);
                    if (i == 0) { // 只打印第一个要素的属性信息
                        log.info("原始属性[{}]: {} = {}", i, propertyName, value);
                    }
                }
            }

            // 根据模板映射转换字段名
            Map<String, Object> mappedAttributes = new HashMap<>();
            if (fieldMappings != null) {
                for (Map<String, Object> mapping : fieldMappings) {
                    String fieldName = (String) mapping.get("fieldName");
                    String shpFieldName = (String) mapping.get("shpFieldName");
                    Boolean checked = (Boolean) mapping.get("checked");

                    if (fieldName != null && shpFieldName != null && (checked == null || checked)) {
                        Object value = attributes.get(shpFieldName);
                        if (value != null) {
                            mappedAttributes.put(fieldName, value);
                            if (i == 0) { // 只打印第一个要素的映射信息
                                log.info("字段映射[{}]: {} ({}) -> {} = {}",
                                         i, shpFieldName, fieldName, fieldName, value);
                            }
                        } else {
                            if (i == 0) { // 只打印第一个要素的缺失信息
                                log.warn("字段映射[{}]: SHP字段 '{}' 未找到值，可用字段: {}",
                                         i, shpFieldName, attributes.keySet());
                            }
                        }
                    }
                }
            }

            // 同时保留原始属性和映射后的属性
            mappedAttributes.putAll(attributes);
            entity.setRawAttributes(mappedAttributes);

            if (i == 0) { // 只打印第一个要素的最终属性
                log.info("最终属性[{}]: {}", i, mappedAttributes.keySet());
            }

            geoEntities.add(entity);
        }

        log.info("SimpleFeature转换完成 - 输入: {}, 输出: {}", features.size(), geoEntities.size());
        return geoEntities;
    }

}
