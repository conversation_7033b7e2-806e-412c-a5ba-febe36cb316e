package com.zjxy.gisimportservice.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.mapper.GisManageTemplateMapper;
import com.zjxy.gisimportservice.service.GisManageTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * GIS管理模板服务实现类
 */
@Slf4j
@Service
public class GisManageTemplateServiceImpl extends ServiceImpl<GisManageTemplateMapper, GisManageTemplate>
        implements GisManageTemplateService {
    @Resource
    private GisManageTemplateMapper templateMapper;


    @Override
    public GisManageTemplate getTemplateById(Integer templateId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        if (templateId == null) {
            throw new IllegalArgumentException("模板ID不能为空");
        }

        GisManageTemplate template = templateMapper.selectById(templateId);
        if (template == null) {
            throw new RuntimeException("未找到ID为 " + templateId + " 的模板");
        }

        // 详细调试日志
        log.info("=== 模板详细信息调试 ===");
        log.info("模板ID: {}", template.getId());
        log.info("模板名称: {}", template.getNameZh());
        log.info("目标表名: {}", template.getTableName());
        log.info("源坐标系: {}", template.getOriginalCoordinateSystem());
        log.info("目标坐标系: {}", template.getTargetCoordinateSystem());
        log.info("坐标转换: {}", template.getIsZh());
        log.info("模板类型: {}", template.getType());
        log.info("数据库名: {}", template.getDataBase());
        log.info("字段映射: {}", template.getMap());
        log.info("是否使用自定义表: {}", template.getTableName() != null &&
                !template.getTableName().trim().isEmpty() &&
                !"geo_features".equals(template.getTableName()));
        log.info("=== 模板调试信息结束 ===");

        return template;
    }

    @Override
    public List<GisManageTemplate> getTemplatesByTableName(String tableName) {
        if (!StringUtils.hasText(tableName)) {
            throw new IllegalArgumentException("表名不能为空");
        }

        List<GisManageTemplate> templates = templateMapper.selectByTableName(tableName);
        log.info("根据表名 {} 查询到 {} 个模板", tableName, templates.size());
        return templates;
    }


    @Override
    public List<GisManageTemplate> getAllImportTemplates() {
        DynamicDataSourceManager.build().useDataSource("slave");
        List<GisManageTemplate> templates = templateMapper.selectAllImportTemplates();
        log.info("查询到 {} 个导入模板", templates.size());
        return templates;
    }

    @Override
    public Integer createTemplate(GisManageTemplate template) {
        DynamicDataSourceManager.build().useDataSource("master");

        if (template == null) {
            throw new IllegalArgumentException("模板信息不能为空");
        }

        // 设置创建时间
        template.setCreateTime(new Date());

        // 设置默认值
        if (template.getInOrOut() == null) {
            template.setInOrOut("in");
        }
        if (template.getTemplateType() == null) {
            template.setTemplateType("shp");
        }

        int result = templateMapper.insert(template);
        if (result > 0) {
            log.info("创建模板成功，模板ID: {}, 模板名称: {}", template.getId(), template.getNameZh());
            return template.getId();
        } else {
            throw new RuntimeException("创建模板失败");
        }
    }

    @Override
    public Boolean updateTemplate(GisManageTemplate template) {
        DynamicDataSourceManager.build().useDataSource("slave");
        if (template == null || template.getId() == null) {
            throw new IllegalArgumentException("模板信息或模板ID不能为空");
        }

        // 验证模板是否存在
        GisManageTemplate existingTemplate = templateMapper.selectById(template.getId());
        if (existingTemplate == null) {
            throw new RuntimeException("要更新的模板不存在");
        }

        int result = templateMapper.updateById(template);
        if (result > 0) {
            log.info("更新模板成功，模板ID: {}", template.getId());
            return true;
        } else {
            log.error("更新模板失败，模板ID: {}", template.getId());
            return false;
        }
    }

    @Override
    public Boolean deleteTemplate(Integer templateId) {
        DynamicDataSourceManager.build().useDataSource("slave");
        if (templateId == null) {
            throw new IllegalArgumentException("模板ID不能为空");
        }

        // 验证模板是否存在
        GisManageTemplate template = templateMapper.selectById(templateId);
        if (template == null) {
            throw new RuntimeException("要删除的模板不存在");
        }

        int result = templateMapper.deleteById(templateId);
        if (result > 0) {
            log.info("删除模板成功，模板ID: {}", templateId);
            return true;
        } else {
            log.error("删除模板失败，模板ID: {}", templateId);
            return false;
        }
    }

    @Override
    public Page<GisManageTemplate> getTemplatesPage(Integer id, String tableName, String nameZh, String dataBase, Long pageSize, Long pageIndex) {
        DynamicDataSourceManager.build().useDataSource("slave");
        Page<GisManageTemplate> page = new Page<>(pageIndex, pageSize);
        QueryWrapper<GisManageTemplate> queryWrapper = new QueryWrapper<>();

        if (id != null) {
            queryWrapper.eq("id", id);
        }
        if (StringUtils.hasText(tableName)) {
            queryWrapper.like("table_name", tableName);
        }
        if (StringUtils.hasText(nameZh)) {
            queryWrapper.like("name_zh", nameZh);
        }
        if (StringUtils.hasText(dataBase)) {
            queryWrapper.eq("data_base", dataBase);
        }

        queryWrapper.orderByAsc("create_time");
        return templateMapper.selectPage(page, queryWrapper);
    }

}
