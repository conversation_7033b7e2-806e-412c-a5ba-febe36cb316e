package com.zjxy.gisimportservice.service.Impl;

import com.alibaba.excel.EasyExcel;
import com.zjxy.framework.manager.DynamicDataSourceManager;
import com.zjxy.gisimportservice.config.ExcelImportConfig;
import com.zjxy.gisimportservice.entity.*;
import com.zjxy.gisimportservice.listener.ExcelDataListener;
import com.zjxy.gisimportservice.monitor.ExcelImportMetrics;
import com.zjxy.gisimportservice.service.*;
import com.zjxy.gisimportservice.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Excel导入服务实现类
 *
 * 实现Excel文件的导入、解析、验证和数据处理功能
 * 集成现有的gisimportservice架构和服务
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-28
 */
@Slf4j
@Service
public class ExcelImportServiceImpl implements ExcelImportService {

    @Autowired
    private GisManageTemplateService templateService;

    @Autowired
    private DataValidationService validationService;

    @Autowired
    private CoordinateTransformService coordinateTransformService;

    @Autowired
    private InsertService batchInsertService;

    @Autowired
    private GisImportTaskService taskService;

    @Autowired
    private ValidationErrorExportService validationErrorExportService;

    @Autowired
    private ExcelImportConfig excelConfig;

    @Autowired
    private ExcelImportMetrics metrics;

    @Value("${gis.import.task.upload.temp-path:/temp/gis-uploads}")
    private String tempPath;

    @Override
    public ExcelImportResult importExcelData(MultipartFile file, Integer templateId,
                                           String target, String createdBy)
                                           throws IOException, ParseException {

        log.info("开始Excel导入 - 文件: {}, 模板ID: {}, 目标: {}",
                file.getOriginalFilename(), templateId, target);

        LocalDateTime startTime = LocalDateTime.now();
        ExcelImportResult.ExcelImportResultBuilder resultBuilder = ExcelImportResult.builder()
                .startTime(startTime)
                .success(false);

        String taskId = "excel_import_" + System.currentTimeMillis();

        try {

            // 2. 获取模板配置
            GisManageTemplate template = getAndValidateTemplate(templateId);

            // 3. 记录导入开始
            metrics.recordImportStart(taskId, file.getOriginalFilename(), file.getSize(), template.getId().toString());

            // 4. 创建导入任务
            GisImportTask task = createImportTask(file, template, createdBy);

            // 5. 执行Excel导入
            ExcelImportResult result = executeExcelImport(file, template, target, task);

            // 6. 更新任务状态
            updateTaskStatus(task, result);

            // 7. 记录导入完成
            metrics.recordImportComplete(taskId, result.isSuccess(), result.getProcessingTimeMs(),
                    result.getTotalRecords(), result.getSuccessRecords(), result.getErrorRecords());

            log.info("Excel导入完成 - 任务ID: {}, 总记录: {}, 成功: {}, 错误: {}",
                    task != null ? task.getId() : "N/A", result.getTotalRecords(), result.getSuccessRecords(), result.getErrorRecords());

            return result;

        } catch (Exception e) {
            log.error("Excel导入失败", e);

            // 记录导入错误
            metrics.recordImportError(taskId, e.getMessage());

            return resultBuilder
                    .success(false)
                    .message("Excel导入失败: " + e.getMessage())
                    .endTime(LocalDateTime.now())
                    .processingTimeMs(calculateProcessingTime(startTime))
                    .build();
        }
    }

    @Override
    public Map<String, Object> analyzeExcelFile(MultipartFile file, Integer headerRow) throws IOException {
        log.info("开始分析Excel文件 - 文件: {}, 表头行: {}", file.getOriginalFilename(), headerRow);

        try {
            // 验证文件
            validateFile(file);

            // 分析Excel结构
            Map<String, Object> analysis = ExcelUtil.analyzeExcelStructure(file, headerRow);

            // 添加额外分析信息
            analysis.put("analysisTime", LocalDateTime.now());
            analysis.put("fileValid", true);

            log.info("Excel文件分析完成 - 工作表数: {}", analysis.get("totalSheets"));
            return analysis;

        } catch (Exception e) {
            log.error("Excel文件分析失败", e);
            throw new IOException("Excel文件分析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getSheetNames(MultipartFile file) throws IOException {
        log.info("获取Excel工作表名称 - 文件: {}", file.getOriginalFilename());

        try {
            validateFile(file);
            List<String> sheetNames = ExcelUtil.getSheetNames(file);
            log.info("获取到 {} 个工作表", sheetNames.size());
            return sheetNames;

        } catch (Exception e) {
            log.error("获取Excel工作表名称失败", e);
            throw new IOException("获取Excel工作表名称失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, List<Map<String, Object>>> analyzeExcelAttachment(MultipartFile file, Integer templateId)
            throws IOException {
        log.info("分析Excel附件数据 - 文件: {}, 模板ID: {}", file.getOriginalFilename(), templateId);

        try {
            // 获取模板配置
            GisManageTemplate template = templateService.getTemplateById(templateId);
            if (template == null) {
                throw new IllegalArgumentException("模板不存在，模板ID: " + templateId);
            }

            // 分析附件数据
            Map<String, List<Map<String, Object>>> result = new HashMap<>();

            // 这里可以根据模板配置分析附件数据
            // 暂时返回空结果，后续可以扩展

            log.info("Excel附件数据分析完成");
            return result;

        } catch (Exception e) {
            log.error("Excel附件数据分析失败", e);
            throw new IOException("Excel附件数据分析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> validateExcelData(MultipartFile file, Integer templateId)
            throws IOException {
        log.info("验证Excel数据 - 文件: {}, 模板ID: {}",
                file.getOriginalFilename(), templateId);

        try {
            // 获取模板配置
            GisManageTemplate template = getAndValidateTemplate(templateId);

            // 执行验证模式的导入
            ExcelImportResult result = executeExcelImport(file, template, "valid", null);

            // 转换为验证结果格式
            Map<String, Object> validationResult = new HashMap<>();
            validationResult.put("valid", result.isSuccess());
            validationResult.put("totalRecords", result.getTotalRecords());
            validationResult.put("errorRecords", result.getErrorRecords());
            validationResult.put("errorRate", result.getErrorRate());
            validationResult.put("errors", result.getErrors());
            validationResult.put("warnings", result.getWarnings());

            log.info("Excel数据验证完成 - 总记录: {}, 错误: {}",
                    result.getTotalRecords(), result.getErrorRecords());

            return validationResult;

        } catch (Exception e) {
            log.error("Excel数据验证失败", e);
            throw new IOException("Excel数据验证失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ExcelImportResult batchImportExcelData(MultipartFile file, Integer templateId,
                                                Integer batchSize, String target, String createdBy)
                                                throws IOException, ParseException {
        log.info("批量导入Excel数据 - 文件: {}, 模板ID: {}, 批次大小: {}",
                file.getOriginalFilename(), templateId, batchSize);

        // 获取模板并设置批次大小
        GisManageTemplate template = getAndValidateTemplate(templateId);

        // 设置Excel配置中的批次大小
        Map<String, Object> excelConfigMap = template.getExcelConfig();
        if (excelConfigMap == null) {
            excelConfigMap = new HashMap<>();
        }
        excelConfigMap.put("batchSize", batchSize);
        template.setExcelConfig(excelConfigMap);

        // 执行导入
        return importExcelData(file, templateId, target, createdBy);
    }

    /**
     * 执行Excel导入的核心逻辑
     */
    private ExcelImportResult executeExcelImport(MultipartFile file, GisManageTemplate template,
                                               String target, GisImportTask task)
                                               throws IOException {

        LocalDateTime startTime = LocalDateTime.now();

        // 从模板配置中获取工作表名称
        String sheetName = template.getSheetName();
        if (sheetName == null || sheetName.trim().isEmpty()) {
            throw new RuntimeException("模板配置缺少工作表名称，请在模板中配置sheet_name字段");
        }

        log.info("使用模板配置的工作表名称: {}", sheetName);

        // 创建结果对象
        ExcelImportResult.ExcelImportResultBuilder resultBuilder = ExcelImportResult.builder()
                .startTime(startTime)
                .taskId(task != null ? task.getId() : null);

        try (InputStream inputStream = file.getInputStream()) {

            // 首先分析Excel文件结构
            analyzeExcelFileStructure(file);

            log.info("目标工作表名称: '{}'", sheetName);

            // 获取批次大小
            int batchSize = template.getExcelBatchSize();
            log.info("批次大小: {}", batchSize);

            // 创建数据监听器
            ExcelDataListener listener = new ExcelDataListener(
                    template,
                    validationService,
                    coordinateTransformService,
                    batchInsertService,
                    batchSize,
                    target
            );

            // ========== EasyExcel配置修复 ==========
            // 修复1：严格验证th_line配置
            Integer thLine = template.getThLine();
            if (thLine == null || thLine <= 0) {
                thLine = 1; // 默认第1行是表头
                log.warn("模板th_line配置无效: {}, 使用默认值1", template.getThLine());
            }

            // 修复2：正确计算headRowNumber
            // headRowNumber表示要跳过的行数，不是表头行号
            // th_line=1 (第1行是表头) -> headRowNumber=0 (不跳过任何行)
            // th_line=2 (第2行是表头) -> headRowNumber=1 (跳过第1行)
            int headRowNumber = thLine - 1;

            // 修复3：增强调试日志
            log.info("=== EasyExcel配置详情 ===");
            log.info("模板ID: {}", template.getId());
            log.info("工作表名: '{}'", sheetName);
            log.info("th_line配置: {} (表头在第{}行)", thLine, thLine);
            log.info("headRowNumber: {} (跳过前{}行)", headRowNumber, headRowNumber);
            log.info("期望表头行索引: {} (0基索引)", headRowNumber);
            log.info("=== 配置详情结束 ===");

            // 打印模板的详细配置
            log.info("模板详细配置:");
            log.info("  模板ID: {}", template.getId());
            log.info("  模板类型: {}", template.getTemplateType());
            log.info("  目标表: {}", template.getTableName());
            log.info("  字段映射数量: {}", template.getMap() != null ? template.getMap().size() : 0);

            // 使用EasyExcel读取指定名称的工作表
            try {
                log.info("开始读取Excel工作表: '{}'", sheetName);
                EasyExcel.read(inputStream, listener)
                        .sheet(sheetName)
                        .headRowNumber(headRowNumber)
                        .doRead();
                log.info("Excel工作表读取完成: '{}'", sheetName);
            } catch (Exception e) {
                // 检查是否是工作表不存在的错误
                String errorMessage = e.getMessage();
                if (errorMessage != null && (errorMessage.contains("sheet") || errorMessage.contains("工作表"))) {
                    throw new RuntimeException("找不到工作表 '" + sheetName + "'，请检查Excel文件中是否存在该工作表");
                } else {
                    throw new RuntimeException("读取Excel工作表 '" + sheetName + "' 时发生错误: " + errorMessage, e);
                }
            }

            log.info("Excel读取完成 - 监听器统计: 总记录={}, 成功={}, 错误={}",
                    listener.getTotalRecords(), listener.getSuccessRecords(), listener.getErrorRecords());

            // 构建结果
            LocalDateTime endTime = LocalDateTime.now();
            long processingTime = calculateProcessingTime(startTime);

            ExcelImportResult result = resultBuilder
                    .success(!listener.isHasError() || "valid".equals(target))
                    .message(listener.isHasError() ? "导入过程中发现错误" : "导入成功")
                    .totalRecords(listener.getTotalRecords())
                    .successRecords(listener.getSuccessRecords())
                    .errorRecords(listener.getErrorRecords())
                    // .errors(listener.getErrors()) // 暂时注释，类型不匹配
                    .endTime(endTime)
                    .processingTimeMs(processingTime)
                    .fileInfo(createFileInfo(file, sheetName))
                    .templateInfo(createTemplateInfo(template))
                    .build();

            return result;

        } catch (Exception e) {
            log.error("执行Excel导入失败", e);
            throw new IOException("执行Excel导入失败: " + e.getMessage(), e);
        }
    }


    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        // 检查文件格式
        if (!excelConfig.isSupportedFormat(file.getOriginalFilename())) {
            throw new IllegalArgumentException("不支持的文件格式，支持的格式: " + excelConfig.getSupportedFormats());
        }

        // 检查文件大小
        if (!excelConfig.isFileSizeValid(file.getSize())) {
            throw new IllegalArgumentException("文件大小超过限制，最大允许: " + excelConfig.getMaxFileSize() + "MB");
        }
    }

    /**
     * 获取并验证模板
     */
    private GisManageTemplate getAndValidateTemplate(Integer templateId) {
        GisManageTemplate template = templateService.getTemplateById(templateId);
        if (template == null) {
            throw new IllegalArgumentException("模板不存在，模板ID: " + templateId);
        }

        // 验证模板是否支持Excel
        if (!template.isExcelTemplate() && !"excel".equalsIgnoreCase(template.getTemplateType())) {
            log.warn("模板类型不是Excel，但仍然尝试处理，模板ID: {}, 类型: {}", templateId, template.getTemplateType());
        }

        return template;
    }

    /**
     * 创建导入任务
     */
    private GisImportTask createImportTask(MultipartFile file, GisManageTemplate template, String createdBy) {
        if (createdBy == null) {
            return null; // 验证模式可能不需要创建任务
        }

        return taskService.createImportTask(
                "Excel导入-" + file.getOriginalFilename(),
                template.getId(),
                GisImportTask.ImportFormat.EXCEL,
                file.getOriginalFilename(),
                file.getSize(),
                createdBy
        );
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(GisImportTask task, ExcelImportResult result) {
        if (task == null) {
            return;
        }

        GisImportTask.DataStatus status = result.isSuccess() ?
                GisImportTask.DataStatus.DATA_IMPORTED :
                GisImportTask.DataStatus.NOT_IMPORTED;

        taskService.updateTaskStatus(task.getId(), status);
        // 更新任务统计信息（如果服务支持的话）
        try {
            // 这里可以调用更新统计的方法，如果GisImportTaskService有相关方法
            log.debug("更新任务统计 - 任务ID: {}, 成功: {}, 错误: {}",
                    task.getId(), result.getSuccessRecords(), result.getErrorRecords());
        } catch (Exception e) {
            log.warn("更新任务统计失败", e);
        }
    }

    /**
     * 计算处理时间
     */
    private long calculateProcessingTime(LocalDateTime startTime) {
        return java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
    }

    /**
     * 创建文件信息
     */
    private ExcelImportResult.FileInfo createFileInfo(MultipartFile file, String sheetName) {
        return ExcelImportResult.FileInfo.builder()
                .fileName(file.getOriginalFilename())
                .fileSize(file.getSize())
                .sheetName(sheetName)
                .fileType(file.getContentType())
                .build();
    }

    /**
     * 创建模板信息
     */
    private ExcelImportResult.TemplateInfo createTemplateInfo(GisManageTemplate template) {
        String coordinateTransform = null;
        if (template.getIsZh() != null && template.getIsZh()) {
            coordinateTransform = template.getOriginalCoordinateSystem() + " -> " + template.getTargetCoordinateSystem();
        }

        return ExcelImportResult.TemplateInfo.builder()
                .templateId(template.getId())
                .templateName(template.getNameZh())
                .tableName(template.getTableName())
                .datasourceName(template.getDatasourceName())
                .coordinateTransform(coordinateTransform)
                .build();
    }

    /**
     * 分析Excel文件结构（用于调试）
     */
    private void analyzeExcelFileStructure(MultipartFile file) {
        try {
            log.info("=== 开始分析Excel文件结构 ===");
            log.info("文件名: {}", file.getOriginalFilename());
            log.info("文件大小: {} bytes", file.getSize());
            log.info("文件类型: {}", file.getContentType());

            // 获取工作表名称
            List<String> sheetNames = ExcelUtil.getSheetNames(file);
            log.info("工作表数量: {}", sheetNames.size());
            for (int i = 0; i < sheetNames.size(); i++) {
                log.info("  工作表{}: '{}'", i, sheetNames.get(i));
            }

            // 分析第一个工作表的结构
            if (!sheetNames.isEmpty()) {
                Map<String, Object> analysis = ExcelUtil.analyzeExcelStructure(file, 1);
                log.info("第一个工作表分析结果: {}", analysis);
            }

            log.info("=== Excel文件结构分析完成 ===");
        } catch (Exception e) {
            log.error("分析Excel文件结构失败: {}", e.getMessage(), e);
        }
    }

    // ==================== 任务驱动模式新增方法实现 ====================

    @Override
    public String saveUploadedFile(MultipartFile file, String taskId) throws IOException {
        try {
            log.info("保存上传文件 - 任务ID: {}, 文件名: {}", taskId, file.getOriginalFilename());

            // 创建文件保存目录
            String uploadDir = tempPath;
            java.io.File dir = new java.io.File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String fileName = "excel_" + taskId + "_" + System.currentTimeMillis() + extension;
            String filePath = uploadDir + java.io.File.separator + fileName;

            // 保存文件
            java.io.File targetFile = new java.io.File(filePath);
            file.transferTo(targetFile);

            log.info("文件保存成功 - 路径: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("保存上传文件失败 - 任务ID: {}", taskId, e);
            throw new IOException("文件保存失败: " + e.getMessage(), e);
        }
    }

    @Override
    public ValidationResult validateExcelData(String filePath, GisManageTemplate template,
                                            String target, String createdBy, Long taskId) {
        try {
            log.info("开始Excel数据验证 - 文件: {}, 模板ID: {}, 目标: {}", filePath, template.getId(), target);

            // 设置数据源
            if (template.getDataBase() != null) {
                DynamicDataSourceManager.build().useDataSource(template.getDataBase());
            }

            // 从模板配置中获取工作表名称
            String sheetName = template.getSheetName();
            if (sheetName == null || sheetName.trim().isEmpty()) {
                throw new RuntimeException("模板配置缺少工作表名称，请在模板中配置sheet_name字段");
            }

            log.info("使用模板配置的工作表名称: {}", sheetName);

            // 创建验证结果收集器（简化初始化，实际值由listener提供）
            ValidationResult result = new ValidationResult();

            // 根据target模式创建不同的Excel数据监听器
            ExcelDataListener listener;
            if ("valid".equals(target)) {
                // 纯验证模式：只验证，不插入数据库
                log.info("Excel纯验证模式 - 只验证数据，不执行数据库操作");
                listener = new ExcelDataListener(
                    template,
                    validationService,
                    coordinateTransformService,
                    batchInsertService,
                    1000,  // 批次大小
                    target
                );
                // 设置验证模式标识，确保不执行数据库插入
                listener.setValidationOnly(true);
            } else if ("import".equals(target)) {
                // 直接导入模式：跳过验证，直接导入
                log.info("Excel直接导入模式 - 跳过验证，直接导入数据库");
                listener = new ExcelDataListener(
                    template,
                    validationService,
                    coordinateTransformService,
                    batchInsertService,
                    1000,  // 批次大小
                    target
                );
                // 设置跳过验证标识，提高导入性能
                listener.setSkipValidation(true);
            } else {
                throw new IllegalArgumentException("不支持的目标模式: " + target);
            }

            // 读取Excel文件
            java.io.File file = new java.io.File(filePath);
            if (!file.exists()) {
                throw new RuntimeException("文件不存在: " + filePath);
            }

            // ========== 修复EasyExcel配置（validateExcelData方法）==========
            // 计算正确的headRowNumber配置
            Integer thLine = template.getThLine();
            if (thLine == null || thLine <= 0) {
                thLine = 1; // 默认第1行是表头
                log.warn("validateExcelData: 模板th_line配置无效: {}, 使用默认值1", template.getThLine());
            }

            int headRowNumber = thLine - 1; // 转换为跳过行数

            log.info("=== validateExcelData EasyExcel配置 ===");
            log.info("模板ID: {}", template.getId());
            log.info("工作表名: '{}'", sheetName);
            log.info("th_line配置: {} (表头在第{}行)", thLine, thLine);
            log.info("headRowNumber: {} (跳过前{}行)", headRowNumber, headRowNumber);
            log.info("期望表头行索引: {} (0基索引)", headRowNumber);
            log.info("=== 配置详情结束 ===");

            // 使用EasyExcel读取指定名称的工作表
            try {
                log.info("开始读取Excel工作表: {}", sheetName);
                EasyExcel.read(file, listener)
                        .sheet(sheetName)
                        .headRowNumber(headRowNumber)  // 关键修复：添加headRowNumber配置
                        .doRead();
                log.info("Excel工作表读取完成: {}", sheetName);
            } catch (Exception e) {
                // 检查是否是工作表不存在的错误
                String errorMessage = e.getMessage();
                if (errorMessage != null && (errorMessage.contains("sheet") || errorMessage.contains("工作表"))) {
                    throw new RuntimeException("找不到工作表 '" + sheetName + "'，请检查Excel文件中是否存在该工作表");
                } else {
                    throw new RuntimeException("读取Excel工作表 '" + sheetName + "' 时发生错误: " + errorMessage, e);
                }
            }

            // 获取验证结果（移除旧的简单错误列表，使用新的专业错误报告系统）
            result.setTotalRecords(listener.getTotalRecords());
            result.setValidRecords(listener.getSuccessRecords());
            result.setErrorRecords(listener.getErrorRecords());
            result.setPassed(listener.getErrorRecords() == 0);

            // 注意：不再设置简单的errors列表，新系统使用DetailedValidationError和专业错误报告

            if (result.getTotalRecords() > 0) {
                result.setErrorRate((double) result.getErrorRecords() / result.getTotalRecords() * 100);
            }

            log.info("Excel数据验证完成 - 总记录: {}, 有效: {}, 错误: {}, 通过: {}",
                    result.getTotalRecords(), result.getValidRecords(), result.getErrorRecords(), result.isPassed());

            // 如果有详细错误，生成专业的Excel错误标注文件
            if (!listener.getDetailedErrors().isEmpty()) {
                try {
                    // 使用新的基于原文件标注的错误报告导出功能
                    String taskName = getTaskDisplayName(template);

                    // 获取表头行配置（重用已有的thLine变量）
                    int headerRowIndex = (thLine != null && thLine > 0) ? thLine - 1 : 0; // 转换为0基索引

                    String errorFilePath = validationErrorExportService.exportValidationErrors(
                            listener.getDetailedErrors(),
                            taskId != null ? taskId : template.getId().longValue(),
                            taskName,
                            filePath,
                            headerRowIndex, // 表头行索引参数
                            template); // 添加模板参数
                    result.setErrorFilePath(errorFilePath);
                    log.info("Excel错误标注文件已生成: {}, 错误数量: {}", errorFilePath, listener.getDetailedErrors().size());
                } catch (Exception e) {
                    log.error("生成Excel错误标注文件失败: {}", e.getMessage(), e);
                    // 错误标注文件生成失败，但验证结果仍然有效
                }
            }

            return result;

        } catch (Exception e) {
            log.error("Excel数据验证失败 - 文件: {}", filePath, e);

            ValidationResult errorResult = new ValidationResult();
            errorResult.setTotalRecords(0);
            errorResult.setValidRecords(0);
            errorResult.setErrorRecords(1);
            errorResult.setPassed(false);
            errorResult.setErrors(new ArrayList<>());

            ValidationResult.ValidationError error = new ValidationResult.ValidationError();
            error.setRecordIndex(0);
            error.setFeatureId("SYSTEM_ERROR");
            error.setFieldName("SYSTEM");
            error.setErrorType(ValidationResult.ErrorType.BUSINESS_RULE_VIOLATION);
            error.setErrorMessage("验证过程异常: " + e.getMessage());
            error.setErrorLevel(ValidationResult.ErrorLevel.CRITICAL);
            errorResult.getErrors().add(error);

            return errorResult;
        }
    }

    /**
     * 获取任务显示名称的工具方法
     * 优先使用中文名称，降级使用表名
     *
     * @param template 模板对象
     * @return 任务显示名称
     */
    private String getTaskDisplayName(GisManageTemplate template) {
        if (template.getNameZh() != null && !template.getNameZh().trim().isEmpty()) {
            return template.getNameZh();
        }
        return template.getTableName() != null ? template.getTableName() : "未命名任务";
    }
}
