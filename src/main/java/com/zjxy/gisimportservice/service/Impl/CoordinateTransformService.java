package com.zjxy.gisimportservice.service.Impl;

import com.zjxy.gisimportservice.util.ZbzhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 坐标转换服务
 * 用于在GIS数据导入过程中进行坐标系转换
 */
@Slf4j
@Service
public class CoordinateTransformService {
    /**
     * 转换几何数据的坐标系
     * @param geometryWkt 原始几何数据的WKT字符串
     * @param sourceCoordSystem 源坐标系
     * @param targetCoordSystem 目标坐标系
     * @return 转换后的WKT字符串
     */
    public String transformGeometry(String geometryWkt, String sourceCoordSystem, String targetCoordSystem) {
        if (geometryWkt == null || geometryWkt.trim().isEmpty()) {
            return geometryWkt;
        }

        try {

            if (sourceCoordSystem.equals(targetCoordSystem)) {
                return geometryWkt;
            }

            long startTime = System.currentTimeMillis();
            String transformedWkt = ZbzhUtil.convertSingleGeometry(geometryWkt, sourceCoordSystem, targetCoordSystem);
            long endTime = System.currentTimeMillis();

            // 只在调试模式下输出耗时
            if (log.isDebugEnabled()) {
                log.debug("坐标转换耗时: {}ms, {} -> {}", (endTime - startTime), sourceCoordSystem, targetCoordSystem);
            }

            return transformedWkt;

        } catch (Exception e) {
            log.error("坐标转换失败. WKT: {}, 源坐标系: {}, 目标坐标系: {}",
                     geometryWkt, sourceCoordSystem, targetCoordSystem, e);

        }
        return geometryWkt;
    }

    /**
     * 检查坐标系是否支持
     * @param coordSystem 坐标系名称
     * @return 是否支持
     */
    public boolean isSupportedCoordSystem(String coordSystem) {
        try {
            java.util.Map<String, Object> coordSystems = ZbzhUtil.GetCoordSystem();
            return coordSystems.containsKey(coordSystem);
        } catch (Exception e) {
            log.error("检查坐标系支持状态失败: {}", coordSystem, e);
            return false;
        }
    }

    /**
     * 获取所有支持的坐标系
     * @return 支持的坐标系列表
     */
    public java.util.Set<String> getSupportedCoordSystems() {
        try {
            java.util.Map<String, Object> coordSystems = ZbzhUtil.GetCoordSystem();
            return coordSystems.keySet();
        } catch (Exception e) {
            log.error("获取支持的坐标系列表失败", e);
            return new java.util.HashSet<>();
        }
    }
}
