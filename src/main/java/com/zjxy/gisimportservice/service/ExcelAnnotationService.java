package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GeoFeatureEntity;
import com.zjxy.gisimportservice.entity.GisManageTemplate;
import com.zjxy.gisimportservice.listener.ExcelDataListener;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;
import java.util.Map;

/**
 * Excel错误标注服务接口
 *
 * 提供Excel文件错误标注功能，包括：
 * - SHP文件验证错误的Excel标注
 * - Excel文件验证错误的标注
 * - 错误信息的可视化展示
 * - 错误报告文件的生成和下载
 *
 * <AUTHOR> Data Import System
 * @since 2024-08-01
 */
public interface ExcelAnnotationService {

    /**
     * 生成错误标注文件名
     *
     * @param taskName 任务名称
     * @param taskId 任务ID
     * @return 文件名
     */
    String generateAnnotatedFileName(String taskName, Long taskId);

    /**
     * 获取错误标注文件保存路径
     *
     * @param fileName 文件名
     * @return 完整文件路径
     */
    String getAnnotatedFilePath(String fileName);

    /**
     * 基于原始Excel文件生成错误标注文件
     *
     * @param originalFilePath 原始Excel文件路径
     * @param errors 验证错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param headerRowIndex 表头行索引（从0开始）
     * @param template 模板配置（用于字段映射）
     * @return 生成的标注文件路径
     */
    String generateAnnotatedExcel(String originalFilePath,
                                 List<ExcelDataListener.DetailedValidationError> errors,
                                 Long taskId, String taskName, int headerRowIndex,
                                 GisManageTemplate template);

    /**
     * 为SHP文件生成Excel错误标注文件
     *
     * @param entities SHP数据实体列表
     * @param errors 验证错误列表
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param template 模板配置
     * @return 生成的标注文件路径
     */
    String generateShpErrorExcel(List<GeoFeatureEntity> entities,
                                List<ExcelDataListener.DetailedValidationError> errors,
                                Long taskId, String taskName,
                                GisManageTemplate template);
}
