package com.zjxy.gisimportservice.service;

import com.zjxy.gisimportservice.entity.GisManageTemplateValid;

import java.util.List;

/**
 * 字段验证服务接口
 *
 * <AUTHOR> Data Import System
 * @since 2024-07-29
 */
public interface GisManageTemplateValidService {

    /**
     * 添加字段验证规则
     *
     * @param valid 验证规则
     * @return 新增记录的ID
     */
    Integer addValid(GisManageTemplateValid valid);

    /**
     * 批量添加字段验证规则
     *
     * @param validList 验证规则列表
     */
    void addValidBatch(List<GisManageTemplateValid> validList);

    /**
     * 更新字段验证规则
     *
     * @param valid 验证规则
     * @return 是否更新成功
     */
    Boolean updateValid(GisManageTemplateValid valid);

    /**
     * 批量更新字段验证规则
     *
     * @param validList 验证规则列表
     * @return 是否更新成功
     */
    Boolean updateValidBatch(List<GisManageTemplateValid> validList);

    /**
     * 获取字段验证规则
     *
     * @param uid 模板唯一标识
     * @param templateId 模板ID
     * @return 验证规则列表
     */
    List<GisManageTemplateValid> getValid(String uid, String templateId);

    /**
     * 删除字段验证规则
     *
     * @param id 验证规则ID
     * @return 是否删除成功
     */
    Boolean delValid(Integer id);

    /**
     * 通过uid获取对应模版验证规则
     *
     * @param uid 模板唯一标识
     * @return 验证规则列表
     */
    List<GisManageTemplateValid> getValidByUid(String uid);

    /**
     * 根据UID获取单个验证配置
     *
     * @param uid 模板唯一标识
     * @return 验证配置
     */
    GisManageTemplateValid getValidConfigByUid(String uid);

    /**
     * 根据模板ID获取验证配置
     *
     * @param templateId 模板ID
     * @return 验证配置
     */
    GisManageTemplateValid getValidByTemplateId(Integer templateId);
}
