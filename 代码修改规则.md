# GIS导入服务代码修改规则

## 核心原则

### 1. 代码修改优先级
- **优先修改现有代码**：在现有方法基础上进行修改，而非创建全新代码
- **删除冗余方法**：创建新方法前，必须检查并删除不再使用的旧方法
- **保持代码整洁**：避免代码冗余，确保每个方法都有明确的职责

### 2. 方法替换规则
```java
// ❌ 错误做法：直接添加新方法，保留旧方法
public ValidationResult validateTaskDataOld(Long taskId) { ... }  // 保留旧方法
public ValidationResult validateTaskDataNew(Long taskId) { ... }  // 添加新方法

// ✅ 正确做法：修改现有方法，删除不用的方法
public ValidationResult validateTaskData(Long taskId) { 
    // 修改现有方法实现
}
```

### 3. 状态管理规则
基于`data_status`字段实现严格的状态控制：
- **data_status=1**：数据未检查（初始状态）
- **data_status=0**：数据未导入（验证完成）  
- **data_status=2**：数据已导入（导入完成）

## 具体修改规则

### 1. DataValidationServiceImpl.java 修改规则

#### 1.1 方法修改策略
```java
// 现有方法需要修改为支持两阶段模式
@Override
public ValidationResult validateTaskData(Long taskId) {
    // 修改：添加状态检查
    // 修改：根据任务状态决定执行验证还是导入
    // 保留：现有的验证逻辑
}

// 需要删除的方法（如果存在类似功能）
// - validateTaskDataOld()
// - validateDataOld() 
// - 任何带有"Old"、"Backup"、"Temp"后缀的方法
```

#### 1.2 新增方法规则
```java
// 只有在确实需要时才添加新方法
// 新方法必须有明确的职责分工

// ✅ 允许添加的方法类型
private ValidationResult performPureValidation(Long taskId) { ... }     // 纯验证
private ValidationResult performDirectImport(Long taskId) { ... }       // 直接导入
private boolean canValidate(Long taskId) { ... }                       // 状态检查
private boolean canImport(Long taskId) { ... }                         // 状态检查

// ❌ 不允许添加的方法类型
public ValidationResult validateTaskDataV2(Long taskId) { ... }         // 版本化方法
public ValidationResult validateTaskDataBackup(Long taskId) { ... }     // 备份方法
```

### 2. 代码清理规则

#### 2.1 方法清理检查清单
在添加新方法前，必须检查以下内容：
- [ ] 是否存在功能相似的旧方法？
- [ ] 旧方法是否还被其他地方调用？
- [ ] 是否可以直接修改旧方法而非创建新方法？
- [ ] 新方法的职责是否与现有方法重复？

#### 2.2 删除方法的安全步骤
```java
// 步骤1：标记为废弃
@Deprecated
public ValidationResult oldMethod() {
    log.warn("此方法已废弃，请使用新的实现");
    // 可以暂时调用新方法保持兼容性
    return newMethod();
}

// 步骤2：确认无调用后删除
// 使用IDE搜索方法引用，确认无调用后删除
```

### 3. 状态管理代码规则

#### 3.1 状态检查规则
```java
// ✅ 正确的状态检查
private boolean canExecuteValidation(Long taskId) {
    GisImportTask task = gisImportTaskService.getById(taskId);
    return task != null && task.getDataStatus() == 1; // 数据未检查
}

private boolean canExecuteImport(Long taskId) {
    GisImportTask task = gisImportTaskService.getById(taskId);
    return task != null && task.getDataStatus() == 0; // 数据未导入
}

// ❌ 错误的状态检查
private boolean canExecute(Long taskId, String operation) {
    // 过于复杂的通用方法
}
```

#### 3.2 状态更新规则
```java
// ✅ 明确的状态更新
private void updateToValidated(Long taskId, ValidationResult result) {
    GisImportTask task = gisImportTaskService.getById(taskId);
    task.setDataStatus(0); // 明确设置为"数据未导入"
    task.setValidationResult(JSON.toJSONString(result));
    gisImportTaskService.updateById(task);
}

// ❌ 模糊的状态更新
private void updateStatus(Long taskId, int status, Object result) {
    // 过于通用，职责不明确
}
```

### 4. 性能优化规则

#### 4.1 验证模式优化
```java
// ✅ 纯验证模式：只验证，不插入数据库
private ValidationResult performPureValidation(Long taskId) {
    // 1. 读取数据到内存
    // 2. 执行各种验证逻辑
    // 3. 生成验证报告
    // 4. 更新任务状态为"验证完成"
    // ❌ 不执行数据库插入操作
}

// ✅ 导入模式：跳过验证，直接导入
private ValidationResult performDirectImport(Long taskId) {
    // 1. 检查前置条件（必须已验证）
    // 2. 直接读取数据并插入数据库
    // 3. 更新任务状态为"导入完成"
    // ❌ 不重复执行验证逻辑
}
```

### 5. 错误处理规则

#### 5.1 异常处理规范
```java
// ✅ 明确的异常处理
public ValidationResult validateTaskData(Long taskId) {
    try {
        // 检查任务状态
        if (!canValidate(taskId)) {
            throw new IllegalStateException("任务状态不允许执行验证操作");
        }
        
        // 执行验证逻辑
        return performValidation(taskId);
        
    } catch (IllegalStateException e) {
        log.error("任务状态错误 - 任务ID: {}, 错误: {}", taskId, e.getMessage());
        return createErrorResult("状态错误: " + e.getMessage());
    } catch (Exception e) {
        log.error("验证失败 - 任务ID: {}", taskId, e);
        return createErrorResult("验证失败: " + e.getMessage());
    }
}
```

### 6. 日志记录规则

#### 6.1 日志级别规范
```java
// ✅ 正确的日志记录
log.info("开始执行验证 - 任务ID: {}, 模式: {}", taskId, "纯验证模式");
log.debug("验证进度 - 任务ID: {}, 进度: {}/{}", taskId, processed, total);
log.warn("任务状态异常 - 任务ID: {}, 当前状态: {}", taskId, currentStatus);
log.error("验证失败 - 任务ID: {}", taskId, exception);

// ❌ 错误的日志记录
log.info("开始验证"); // 缺少关键信息
log.error("失败了"); // 信息不明确
```

### 7. 代码审查检查清单

#### 7.1 提交前检查
- [ ] 是否删除了不再使用的旧方法？
- [ ] 新方法是否有明确的职责？
- [ ] 是否正确实现了状态管理？
- [ ] 是否添加了适当的日志记录？
- [ ] 是否处理了所有可能的异常情况？
- [ ] 是否符合性能优化要求？

#### 7.2 代码质量标准
- **方法长度**：单个方法不超过50行
- **职责单一**：每个方法只做一件事
- **命名清晰**：方法名能准确反映功能
- **注释完整**：复杂逻辑必须有注释说明

## 实施步骤

### 第一步：分析现有代码
1. 识别需要修改的现有方法
2. 找出功能重复的方法
3. 确定可以删除的废弃方法

### 第二步：制定修改计划
1. 列出需要修改的方法清单
2. 列出需要删除的方法清单
3. 列出需要新增的方法清单

### 第三步：执行修改
1. 先修改现有方法
2. 删除废弃方法
3. 最后添加必要的新方法

### 第四步：测试验证
1. 单元测试覆盖所有修改的方法
2. 集成测试验证业务流程
3. 性能测试确认优化效果

## 注意事项

1. **向后兼容**：修改现有方法时要考虑向后兼容性
2. **渐进式修改**：大的修改要分步骤进行
3. **充分测试**：每次修改后都要进行充分测试
4. **文档更新**：修改代码的同时更新相关文档

遵循这些规则，确保代码修改过程中保持代码库的整洁性和可维护性。
