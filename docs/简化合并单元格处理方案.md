# 简化合并单元格处理方案

## 问题描述

在读取Excel文件的第三行（th_line行）数据时，如果某些列是合并单元格，EasyExcel只会读取合并区域的第一个单元格的值，其他单元格返回null或空值。

例如：
- 第1行：管线编号 | 管线信息（合并） | | 坐标信息
- 第2行：2500     | 详细信息（合并） | | 起点坐标  
- 第3行：管线编号 | 管线类型（合并） | | 起点坐标

在第3行中，如果第2-3列是合并单元格，EasyExcel只能读取到第2列的值"管线类型"，第3列会是null。

## 简化解决方案

### 核心思路

**简单策略**：如果某列为空，使用上一行同列的值填充（适用于纵向合并的单元格）

### 实现方式

在`ExcelDataListener`中添加：

1. **存储上一行数据**：
```java
private Map<Integer, Object> previousRowData = new HashMap<>();
```

2. **简单填充方法**：
```java
private Map<Integer, Object> fillMergedCellValues(Map<Integer, Object> rowData) {
    if (rowData == null || rowData.isEmpty()) {
        return rowData;
    }

    Map<Integer, Object> processedData = new HashMap<>(rowData);
    boolean hasChanges = false;
    
    // 检查每一列，如果为空则尝试从上一行获取值
    for (Map.Entry<Integer, Object> entry : rowData.entrySet()) {
        Integer colIndex = entry.getKey();
        Object cellValue = entry.getValue();
        
        // 如果当前单元格为空或null
        if (cellValue == null || (cellValue instanceof String && ((String) cellValue).trim().isEmpty())) {
            // 尝试从上一行同列获取值
            Object previousValue = previousRowData.get(colIndex);
            if (previousValue != null && !previousValue.toString().trim().isEmpty()) {
                processedData.put(colIndex, previousValue);
                hasChanges = true;
                log.debug("从上一行填充合并单元格值: 列{} = '{}'", colIndex, previousValue);
            }
        }
    }
    
    // 保存当前行数据供下一行使用
    previousRowData = new HashMap<>(processedData);
    
    if (hasChanges) {
        log.debug("合并单元格处理完成，从上一行填充了空值");
    }
    
    return processedData;
}
```

3. **在数据处理前调用**：
```java
@Override
public void invoke(Map<Integer, Object> data, AnalysisContext context) {
    // ... 其他代码 ...
    
    // 简单的合并单元格处理
    data = fillMergedCellValues(data);
    
    // ... 继续处理数据 ...
}
```

## 处理流程

```
第1行数据: {0: "管线编号", 1: "管线信息", 2: null, 3: "坐标信息"}
↓ 保存到 previousRowData

第2行数据: {0: "2500", 1: "详细信息", 2: null, 3: "起点坐标"}
↓ 处理：列2为空，从上一行获取"管线信息"
↓ 结果: {0: "2500", 1: "详细信息", 2: "管线信息", 3: "起点坐标"}
↓ 保存到 previousRowData

第3行数据: {0: "管线编号", 1: "管线类型", 2: null, 3: "起点坐标"}
↓ 处理：列2为空，从上一行获取"详细信息"
↓ 结果: {0: "管线编号", 1: "管线类型", 2: "详细信息", 3: "起点坐标"}
```

## 优点

1. **简单直接**：不需要复杂的POI操作
2. **性能好**：只是简单的内存操作
3. **无侵入**：对现有代码影响最小
4. **容错性强**：即使处理失败也不影响正常流程

## 适用场景

- 纵向合并单元格（上下合并）
- 表头行包含合并单元格
- 数据行包含合并单元格

## 限制

- 只适用于纵向合并，不适用于横向合并
- 假设合并单元格的值在第一行
- 不能处理复杂的合并模式

## 使用示例

假设Excel文件结构：
```
行1: 管线编号 | 管线信息 |          | 坐标信息
行2: 2500    | 详细信息 |          | 起点坐标
行3: 管线编号 | 管线类型 |          | 起点坐标  ← th_line行
行4: 2500    | TR1     | TR1      | 492670.923
```

处理第3行时：
- 原始数据：`{0: "管线编号", 1: "管线类型", 2: null, 3: "起点坐标"}`
- 处理后：`{0: "管线编号", 1: "管线类型", 2: "详细信息", 3: "起点坐标"}`

这样就能正确读取到第3行的完整数据，包括合并单元格的值。

## 总结

这个简化方案通过"从上一行填充空值"的策略，有效解决了Excel合并单元格读取问题，特别适用于表头行包含合并单元格的场景。方案简单、高效、可靠。
