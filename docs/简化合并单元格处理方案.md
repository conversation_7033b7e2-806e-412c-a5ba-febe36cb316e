# 合并单元格处理方案（基于th_line缓存策略）

## 问题描述

在读取Excel文件的第三行（th_line行）数据时，如果某些列是合并单元格，EasyExcel只会读取合并区域的第一个单元格的值，其他单元格返回null或空值。

例如：
- 第1行：管线编号 | 管线信息（合并） | | 坐标信息
- 第2行：2500     | 详细信息（合并） | | 起点坐标
- 第3行：管线编号 | 管线类型（合并） | | 起点坐标  ← th_line行

在第3行中，如果第2-3列是合并单元格，EasyExcel只能读取到第2列的值"管线类型"，第3列会是null。

## 解决方案

### 核心思路

**智能缓存策略**：
1. 当 `th_line > 1` 时，缓存前几行的数据
2. 在读取表头行（th_line行）时，如果某列为空，从缓存的行数据中查找非空值填充

### 实现方式

在`ExcelDataListener`中添加：

1. **缓存前几行数据**：
```java
// 缓存前几行的数据，用于合并单元格处理
private final List<Map<Integer, Object>> cachedRowsData = new ArrayList<>();

// 是否需要缓存行数据（当th_line > 1时）
private boolean needCacheRows = false;
```

2. **构造函数中初始化**：
```java
// 检查是否需要缓存行数据
Integer thLine = template.getThLine();
if (thLine != null && thLine > 1) {
    this.needCacheRows = true;
    log.info("th_line = {}, 将缓存前{}行数据用于合并单元格处理", thLine, thLine - 1);
}
```

3. **智能填充方法**：
```java
private Map<Integer, Object> fillMergedCellValues(Map<Integer, Object> rowData, int currentRowIndex) {
    if (rowData == null || rowData.isEmpty() || !needCacheRows) {
        return rowData;
    }

    Integer thLine = template.getThLine();
    int expectedHeaderIndex = thLine - 1; // 转换为0基索引

    // 如果当前行是表头行，进行合并单元格处理
    if (currentRowIndex == expectedHeaderIndex) {
        return processHeaderRowMergedCells(rowData);
    }

    // 如果当前行在表头行之前，缓存数据
    if (currentRowIndex < expectedHeaderIndex) {
        cachedRowsData.add(new HashMap<>(rowData));
        log.debug("缓存第{}行数据用于合并单元格处理", currentRowIndex + 1);
    }

    return rowData;
}
```

4. **在数据处理前调用**：
```java
@Override
public void invoke(Map<Integer, Object> data, AnalysisContext context) {
    // 合并单元格处理
    int currentRowIndex = context.readRowHolder().getRowIndex();
    data = fillMergedCellValues(data, currentRowIndex);

    // ... 继续处理数据 ...
}
```

## 处理流程

假设 `th_line = 3`（第3行是表头）：

```
第1行数据: {0: "管线编号", 1: "管线信息", 2: null, 3: "坐标信息"}
↓ 缓存到 cachedRowsData[0]

第2行数据: {0: "2500", 1: "详细信息", 2: null, 3: "起点坐标"}
↓ 缓存到 cachedRowsData[1]

第3行数据（表头行）: {0: "管线编号", 1: "管线类型", 2: null, 3: "起点坐标"}
↓ 检测到是表头行，开始处理合并单元格
↓ 列2为空，从缓存中查找：
  - 检查第2行：cachedRowsData[1][2] = null
  - 检查第1行：cachedRowsData[0][2] = null（但第1行第1列有"管线信息"）
↓ 使用找到的值填充
↓ 结果: {0: "管线编号", 1: "管线类型", 2: "管线信息", 3: "起点坐标"}
```

## 优点

1. **简单直接**：不需要复杂的POI操作
2. **性能好**：只是简单的内存操作
3. **无侵入**：对现有代码影响最小
4. **容错性强**：即使处理失败也不影响正常流程

## 适用场景

- 纵向合并单元格（上下合并）
- 表头行包含合并单元格
- 数据行包含合并单元格

## 限制

- 只适用于纵向合并，不适用于横向合并
- 假设合并单元格的值在第一行
- 不能处理复杂的合并模式

## 使用示例

假设Excel文件结构：
```
行1: 管线编号 | 管线信息 |          | 坐标信息
行2: 2500    | 详细信息 |          | 起点坐标
行3: 管线编号 | 管线类型 |          | 起点坐标  ← th_line行
行4: 2500    | TR1     | TR1      | 492670.923
```

处理第3行时：
- 原始数据：`{0: "管线编号", 1: "管线类型", 2: null, 3: "起点坐标"}`
- 处理后：`{0: "管线编号", 1: "管线类型", 2: "详细信息", 3: "起点坐标"}`

这样就能正确读取到第3行的完整数据，包括合并单元格的值。

## 总结

这个简化方案通过"从上一行填充空值"的策略，有效解决了Excel合并单元格读取问题，特别适用于表头行包含合并单元格的场景。方案简单、高效、可靠。
